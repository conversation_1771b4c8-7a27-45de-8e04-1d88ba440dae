/**
 * Main AI Chat Widget Class
 *
 * This class manages the widget lifecycle, DOM injection, and provides
 * the public API for controlling the widget.
 */

import { render, h } from 'preact'
import { ChatInterface } from '../components/ChatInterface'
import { EventEmitter } from '../utils/EventEmitter'
import { StyleManager } from '../utils/StyleManager'
import { generateId } from '../utils/helpers'
import type { WidgetConfig, ChatMessage } from '../types'

export class AIChatWidget extends EventEmitter {
  private config: WidgetConfig
  private container: HTMLElement | null = null
  private shadowRoot: ShadowRoot | HTMLElement | null = null
  private styleManager: StyleManager
  private isOpen: boolean = false
  private isDestroyed: boolean = false
  private instanceId: string

  constructor(config: WidgetConfig) {
    super()

    this.config = this.validateAndNormalizeConfig(config)
    this.instanceId = generateId()
    this.styleManager = new StyleManager(this.config.theme)

    this.initialize()
  }

  /**
   * Initialize the widget
   */
  private initialize(): void {
    if (this.isDestroyed) return

    try {
      this.createContainer()
      this.setupShadowDOM()
      this.injectStyles()
      this.renderWidget()
      this.setupEventListeners()

      // Auto-open if configured
      if (this.config.autoOpen) {
        this.open()
      }

      this.emit('initialized')
    } catch (error) {
      console.error('Failed to initialize AI Chat Widget:', error)
      this.emit('error', error)
    }
  }

  /**
   * Create the widget container element
   */
  private createContainer(): void {
    // Find or create container
    if (this.config.container) {
      this.container = this.config.container
    } else if (this.config.containerId) {
      this.container = document.getElementById(this.config.containerId)
      if (!this.container) {
        throw new Error(`Container element with ID "${this.config.containerId}" not found`)
      }
    } else {
      // Create default container
      this.container = document.createElement('div')
      this.container.id = `ai-chat-widget-${this.instanceId}`
      this.container.className = 'ai-chat-widget-container'
      document.body.appendChild(this.container)
    }

    // Set container styles for positioning
    this.applyContainerStyles()
  }

  /**
   * Setup Shadow DOM for style isolation
   */
  private setupShadowDOM(): void {
    if (!this.container) return

    try {
      this.shadowRoot = this.container.attachShadow({ mode: 'closed' })
    } catch (error) {
      console.warn('Shadow DOM not supported, falling back to regular DOM')
      // Fallback: use the container directly with prefixed styles
      this.shadowRoot = this.container as any
    }
  }

  /**
   * Inject CSS styles into the shadow DOM
   */
  private injectStyles(): void {
    if (!this.shadowRoot) return

    const styleElement = document.createElement('style')
    styleElement.textContent = this.styleManager.getStyles()

    if (this.shadowRoot instanceof ShadowRoot) {
      this.shadowRoot.appendChild(styleElement)
    } else {
      // Fallback for non-shadow DOM
      document.head.appendChild(styleElement)
    }
  }

  /**
   * Render the Preact widget component
   */
  private renderWidget(): void {
    if (!this.shadowRoot) return

    const widgetElement = document.createElement('div')
    widgetElement.className = 'ai-chat-widget-root'

    if (this.shadowRoot instanceof ShadowRoot) {
      this.shadowRoot.appendChild(widgetElement)
    } else {
      this.shadowRoot.appendChild(widgetElement)
    }

    render(
      h(ChatInterface, {
        config: this.config,
        isOpen: this.isOpen,
        onToggle: () => this.toggle(),
        onClose: () => this.close(),
        onMessage: (message: ChatMessage) => this.handleMessage(message),
        onError: (error: Error) => this.handleError(error),
      }),
      widgetElement
    )
  }

  /**
   * Apply positioning styles to the container
   */
  private applyContainerStyles(): void {
    if (!this.container || this.config.position === 'inline') return

    const styles: Partial<CSSStyleDeclaration> = {
      position: 'fixed',
      zIndex: (this.config.zIndex || 9999).toString(),
    }

    switch (this.config.position) {
      case 'bottom-right':
        styles.bottom = '20px'
        styles.right = '20px'
        break
      case 'bottom-left':
        styles.bottom = '20px'
        styles.left = '20px'
        break
      case 'top-right':
        styles.top = '20px'
        styles.right = '20px'
        break
      case 'top-left':
        styles.top = '20px'
        styles.left = '20px'
        break
    }

    Object.assign(this.container.style, styles)
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    // Handle escape key to close widget
    document.addEventListener('keydown', this.handleKeyDown.bind(this))

    // Handle clicks outside widget to close (if configured)
    document.addEventListener('click', this.handleOutsideClick.bind(this))
  }

  /**
   * Handle keyboard events
   */
  private handleKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Escape' && this.isOpen) {
      this.close()
    }
  }

  /**
   * Handle clicks outside the widget
   */
  private handleOutsideClick(event: MouseEvent): void {
    if (!this.container || !this.isOpen) return

    const target = event.target as Node
    if (!this.container.contains(target)) {
      // Don't auto-close on outside click for now
      // this.close()
    }
  }

  /**
   * Handle incoming messages
   */
  private handleMessage(message: ChatMessage): void {
    this.emit('message', message)
    if (this.config.onMessage) {
      this.config.onMessage(message)
    }
  }

  /**
   * Handle errors
   */
  private handleError(error: Error): void {
    this.emit('error', error)
    if (this.config.onError) {
      this.config.onError(error)
    }
  }

  /**
   * Validate and normalize configuration
   */
  private validateAndNormalizeConfig(config: WidgetConfig): WidgetConfig {
    if (!config.apiUrl) {
      throw new Error('apiUrl is required')
    }
    if (!config.token) {
      throw new Error('token is required')
    }

    return {
      position: 'bottom-right',
      showToggleButton: true,
      enableHistory: true,
      enableFeedback: true,
      autoOpen: false,
      zIndex: 9999,
      maxHeight: '600px',
      maxWidth: '400px',
      ...config,
      theme: {
        primaryColor: '#00BFE0',
        secondaryColor: '#1B617A',
        backgroundColor: '#ffffff',
        textColor: '#000000',
        borderRadius: '8px',
        fontFamily: 'system-ui, -apple-system, sans-serif',
        fontSize: '14px',
        ...config.theme,
      },
    }
  }

  // Public API methods
  public open(): void {
    if (this.isDestroyed || this.isOpen) return

    this.isOpen = true
    this.renderWidget() // Re-render with new state
    this.emit('open')

    if (this.config.onOpen) {
      this.config.onOpen()
    }
  }

  public close(): void {
    if (this.isDestroyed || !this.isOpen) return

    this.isOpen = false
    this.renderWidget() // Re-render with new state
    this.emit('close')

    if (this.config.onClose) {
      this.config.onClose()
    }
  }

  public toggle(): void {
    if (this.isOpen) {
      this.close()
    } else {
      this.open()
    }
  }

  public updateConfig(newConfig: Partial<WidgetConfig>): void {
    if (this.isDestroyed) return

    this.config = { ...this.config, ...newConfig }
    this.styleManager.updateTheme(this.config.theme)
    this.injectStyles()
    this.renderWidget()
    this.emit('configUpdated', this.config)
  }

  public destroy(): void {
    if (this.isDestroyed) return

    this.isDestroyed = true

    // Remove event listeners
    document.removeEventListener('keydown', this.handleKeyDown.bind(this))
    document.removeEventListener('click', this.handleOutsideClick.bind(this))

    // Clean up DOM
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container)
    }

    this.emit('destroyed')
    this.removeAllListeners()
  }

  public getConfig(): WidgetConfig {
    return { ...this.config }
  }

  public isWidgetOpen(): boolean {
    return this.isOpen
  }
}
