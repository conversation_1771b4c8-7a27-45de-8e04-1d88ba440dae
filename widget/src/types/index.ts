/**
 * Type definitions for the AI Chat Widget
 */

export interface WidgetConfig {
  // Required configuration
  apiUrl: string
  token: string

  // Optional container configuration
  containerId?: string
  container?: HTMLElement

  // Appearance configuration
  theme?: WidgetTheme
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'inline'
  
  // Behavior configuration
  autoOpen?: boolean
  showToggleButton?: boolean
  enableHistory?: boolean
  enableFeedback?: boolean
  
  // User configuration
  user?: {
    firstName?: string
    resourceId?: string
    [key: string]: any
  }

  // Advanced configuration
  customCSS?: string
  zIndex?: number
  maxHeight?: string
  maxWidth?: string
  
  // Event callbacks
  onOpen?: () => void
  onClose?: () => void
  onMessage?: (message: ChatMessage) => void
  onError?: (error: Error) => void
}

export interface WidgetTheme {
  primaryColor?: string
  secondaryColor?: string
  backgroundColor?: string
  textColor?: string
  borderRadius?: string
  fontFamily?: string
  fontSize?: string
  
  // Component-specific theming
  header?: {
    backgroundColor?: string
    textColor?: string
  }
  
  toggleButton?: {
    backgroundColor?: string
    hoverColor?: string
    size?: string
  }
  
  messages?: {
    userBubbleColor?: string
    botBubbleColor?: string
    userTextColor?: string
    botTextColor?: string
  }
}

export interface ChatMessage {
  id: string
  content: string
  isBot: boolean
  timestamp: Date
  toolInvocations?: ToolInvocation[]
  hasMultipleResults?: boolean
  error?: string
  feedback?: 'like' | 'dislike' | null
}

export interface ToolInvocation {
  toolCallId: string
  toolName: string
  args: Record<string, any>
  result?: any
  state: 'call' | 'result' | 'partial-call'
}

export interface WidgetInstance {
  id: string
  widget: any // Will be typed more specifically later
  open: () => void
  close: () => void
  toggle: () => void
  destroy: () => void
  updateConfig: (config: Partial<WidgetConfig>) => void
  on: (event: string, callback: Function) => void
  off: (event: string, callback: Function) => void
}

export interface APIResponse {
  success: boolean
  data?: any
  error?: string
}

export interface StreamingResponse {
  id: string
  content: string
  role: 'user' | 'assistant'
  toolInvocations?: ToolInvocation[]
}

export interface ThreadInfo {
  id: string
  title: string
  createdAt: string
  updatedAt: string
  resourceId: string
  messageCount?: number
}
