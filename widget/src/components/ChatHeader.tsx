/**
 * Chat Header Component
 * 
 * Header with title, controls, and actions
 */

interface ChatHeaderProps {
  onClose: () => void
  onNewChat: () => void
  messagesCount: number
}

export function ChatHeader({ onClose, onNewChat, messagesCount }: ChatHeaderProps) {
  return (
    <div className="ai-chat-widget-header">
      <div className="ai-chat-widget-header-info">
        <div className="ai-chat-widget-avatar bot">
          <AIIcon />
        </div>
        <div>
          <h3 className="ai-chat-widget-header-title">Luma</h3>
          <p className="ai-chat-widget-header-subtitle">AI Assistant</p>
        </div>
      </div>
      
      <div className="ai-chat-widget-header-actions">
        {messagesCount > 1 && (
          <button
            className="ai-chat-widget-header-button"
            onClick={onNewChat}
            title="Start new conversation"
            aria-label="Start new conversation"
          >
            <NewChatIcon />
          </button>
        )}
        
        <button
          className="ai-chat-widget-close-button"
          onClick={onClose}
          title="Close chat"
          aria-label="Close chat"
        >
          <CloseIcon />
        </button>
      </div>
    </div>
  )
}

function AIIcon() {
  return (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
      <path d="M12 2L2 7l10 5 10-5-10-5z" />
      <path d="M2 17l10 5 10-5" />
      <path d="M2 12l10 5 10-5" />
    </svg>
  )
}

function NewChatIcon() {
  return (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
      <path d="M12 5v14" />
      <path d="M5 12h14" />
    </svg>
  )
}

function CloseIcon() {
  return (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
      <line x1="18" y1="6" x2="6" y2="18" />
      <line x1="6" y1="6" x2="18" y2="18" />
    </svg>
  )
}
