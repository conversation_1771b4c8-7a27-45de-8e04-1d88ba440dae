/**
 * Chat Toggle Button Component
 * 
 * Floating button that opens/closes the chat widget
 */

import type { ComponentProps } from 'preact'

interface ToggleButtonProps {
  isOpen: boolean
  isLoading: boolean
  onClick: () => void
}

export function ToggleButton({ isOpen, isLoading, onClick }: ToggleButtonProps) {
  return (
    <button
      className="ai-chat-widget-toggle-button"
      onClick={onClick}
      aria-label={isOpen ? 'Close chat' : 'Open chat'}
      title={isOpen ? 'Close chat' : 'Chat with AI Assistant'}
    >
      {isLoading ? (
        <LoadingSpinner />
      ) : isOpen ? (
        <CloseIcon />
      ) : (
        <ChatIcon />
      )}
    </button>
  )
}

function ChatIcon() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
      <path d="M8 10h.01" />
      <path d="M12 10h.01" />
      <path d="M16 10h.01" />
    </svg>
  )
}

function CloseIcon() {
  return (
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
      <line x1="18" y1="6" x2="6" y2="18" />
      <line x1="6" y1="6" x2="18" y2="18" />
    </svg>
  )
}

function LoadingSpinner() {
  return (
    <svg 
      width="20" 
      height="20" 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke="currentColor" 
      strokeWidth="2"
      className="ai-chat-widget-spinner"
      style={{ animation: 'spin 1s linear infinite' }}
    >
      <path d="M21 12a9 9 0 11-6.219-8.56" />
    </svg>
  )
}
