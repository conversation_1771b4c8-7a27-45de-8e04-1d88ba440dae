/**
 * Individual Message Item Component
 * 
 * Renders a single message with avatar, content, and actions
 */

import { useState } from 'preact/hooks'
import { <PERSON>downRenderer } from './MarkdownRenderer'
import { copyToClipboard } from '../utils/helpers'
import type { ChatMessage } from '../types'

interface MessageItemProps {
  message: ChatMessage
  onFeedback?: (messageId: string, feedback: 'like' | 'dislike') => void
  onRetry?: () => void
}

export function MessageItem({ message, onFeedback, onRetry }: MessageItemProps) {
  const [showActions, setShowActions] = useState(false)
  const [copySuccess, setCopySuccess] = useState(false)

  const handleCopy = async () => {
    const success = await copyToClipboard(message.content)
    if (success) {
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    }
  }

  const handleFeedback = (feedback: 'like' | 'dislike') => {
    if (onFeedback) {
      onFeedback(message.id, feedback)
    }
  }

  const isInitialMessage = message.id === 'welcome-1'

  return (
    <div 
      className={`ai-chat-widget-message ${message.isBot ? 'bot' : 'user'}`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className={`ai-chat-widget-avatar ${message.isBot ? 'bot' : 'user'}`}>
        {message.isBot ? <BotAvatar /> : <UserAvatar />}
      </div>
      
      <div className="ai-chat-widget-message-content">
        <div className={`ai-chat-widget-message-bubble ${message.isBot ? 'bot' : 'user'}`}>
          {message.isBot ? (
            <MarkdownRenderer content={message.content} />
          ) : (
            <div className="ai-chat-widget-message-text">{message.content}</div>
          )}
          
          {message.error && (
            <div className="ai-chat-widget-error-message">
              <ErrorIcon />
              <span>Failed to send message</span>
              {onRetry && (
                <button 
                  className="ai-chat-widget-retry-button"
                  onClick={onRetry}
                >
                  Retry
                </button>
              )}
            </div>
          )}
        </div>
        
        {/* Message Actions */}
        {message.isBot && (showActions || message.feedback) && !isInitialMessage && (
          <div className="ai-chat-widget-message-actions">
            <button
              className="ai-chat-widget-action-button"
              onClick={handleCopy}
              title={copySuccess ? 'Copied!' : 'Copy message'}
            >
              {copySuccess ? <CheckIcon /> : <CopyIcon />}
            </button>
            
            {onFeedback && (
              <>
                <button
                  className={`ai-chat-widget-action-button ${message.feedback === 'like' ? 'active' : ''}`}
                  onClick={() => handleFeedback('like')}
                  title="Good response"
                >
                  <ThumbsUpIcon />
                </button>
                
                <button
                  className={`ai-chat-widget-action-button ${message.feedback === 'dislike' ? 'active' : ''}`}
                  onClick={() => handleFeedback('dislike')}
                  title="Poor response"
                >
                  <ThumbsDownIcon />
                </button>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

function BotAvatar() {
  return (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
      <path d="M12 2L2 7l10 5 10-5-10-5z" />
      <path d="M2 17l10 5 10-5" />
      <path d="M2 12l10 5 10-5" />
    </svg>
  )
}

function UserAvatar() {
  return (
    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
      <circle cx="12" cy="7" r="4" />
    </svg>
  )
}

function CopyIcon() {
  return (
    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
      <rect x="9" y="9" width="13" height="13" rx="2" ry="2" />
      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" />
    </svg>
  )
}

function CheckIcon() {
  return (
    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
      <polyline points="20,6 9,17 4,12" />
    </svg>
  )
}

function ThumbsUpIcon() {
  return (
    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
      <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3" />
    </svg>
  )
}

function ThumbsDownIcon() {
  return (
    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
      <path d="M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2.31 2.31 0 0 1-2.33 2H17" />
    </svg>
  )
}

function ErrorIcon() {
  return (
    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
      <circle cx="12" cy="12" r="10" />
      <line x1="12" y1="8" x2="12" y2="12" />
      <line x1="12" y1="16" x2="12.01" y2="16" />
    </svg>
  )
}
