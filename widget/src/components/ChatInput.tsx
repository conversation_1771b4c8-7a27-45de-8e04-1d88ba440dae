/**
 * Chat Input Component
 * 
 * Text input with send button and keyboard handling
 */

import { useState, useRef } from 'preact/hooks'

interface ChatInputProps {
  onSendMessage: (content: string) => void
  isLoading: boolean
  placeholder?: string
}

export function ChatInput({ onSendMessage, isLoading, placeholder = "Type your message..." }: ChatInputProps) {
  const [input, setInput] = useState('')
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const handleSubmit = () => {
    const trimmedInput = input.trim()
    if (!trimmedInput || isLoading) return

    onSendMessage(trimmedInput)
    setInput('')
    
    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
    }
  }

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  const handleInput = (e: Event) => {
    const target = e.target as HTMLTextAreaElement
    setInput(target.value)
    
    // Auto-resize textarea
    target.style.height = 'auto'
    target.style.height = `${Math.min(target.scrollHeight, 100)}px`
  }

  return (
    <div className="ai-chat-widget-input-area">
      <div className="ai-chat-widget-input-container">
        <textarea
          ref={textareaRef}
          className="ai-chat-widget-input"
          value={input}
          placeholder={placeholder}
          disabled={isLoading}
          onInput={handleInput}
          onKeyDown={handleKeyDown}
          rows={1}
        />
        
        <button
          className="ai-chat-widget-send-button"
          onClick={handleSubmit}
          disabled={!input.trim() || isLoading}
          title="Send message"
          aria-label="Send message"
        >
          {isLoading ? <LoadingSpinner /> : <SendIcon />}
        </button>
      </div>
    </div>
  )
}

function SendIcon() {
  return (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
      <line x1="22" y1="2" x2="11" y2="13" />
      <polygon points="22,2 15,22 11,13 2,9 22,2" />
    </svg>
  )
}

function LoadingSpinner() {
  return (
    <svg 
      width="16" 
      height="16" 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke="currentColor" 
      strokeWidth="2"
      style={{ animation: 'spin 1s linear infinite' }}
    >
      <path d="M21 12a9 9 0 11-6.219-8.56" />
    </svg>
  )
}
