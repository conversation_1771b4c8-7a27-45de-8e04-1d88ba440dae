/**
 * Main Chat Interface Component
 * 
 * This is the root component that manages the chat UI state and renders
 * either the toggle button or the full chat window.
 */

import { useState, useEffect } from 'preact/hooks'
import { ChatWindow } from './ChatWindow'
import { ToggleButton } from './ToggleButton'
import type { WidgetConfig, ChatMessage } from '../types'

interface ChatInterfaceProps {
  config: WidgetConfig
  isOpen: boolean
  onToggle: () => void
  onClose: () => void
  onMessage: (message: ChatMessage) => void
  onError: (error: Error) => void
}

export function ChatInterface({
  config,
  isOpen,
  onToggle,
  onClose,
  onMessage,
  onError,
}: ChatInterfaceProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [threadId, setThreadId] = useState<string>('')

  // Initialize with welcome message
  useEffect(() => {
    const welcomeMessage: ChatMessage = {
      id: 'welcome-1',
      content: `I'm **Luma** 👋\n\nI'm your AI assistant. I can help you with:\n\n- **Data analysis** and visualization\n- **Business insights** and recommendations\n- **Taking actions** based on your requests\n\nHow can I assist you today?`,
      isBot: true,
      timestamp: new Date(),
      feedback: null,
    }

    // Personalize welcome message if user info is available
    if (config.user?.firstName) {
      welcomeMessage.content = `Hi **${config.user.firstName}**, ${welcomeMessage.content}`
    }

    setMessages([welcomeMessage])
  }, [config.user?.firstName])

  // Generate thread ID on first message
  useEffect(() => {
    if (!threadId) {
      const newThreadId = `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      setThreadId(newThreadId)
    }
  }, [threadId])

  const handleSendMessage = async (content: string) => {
    if (!content.trim() || isLoading) return

    const userMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      content: content.trim(),
      isBot: false,
      timestamp: new Date(),
      feedback: null,
    }

    // Add user message immediately
    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)

    try {
      // Call the streaming API
      await streamChatResponse(content, userMessage.id)
    } catch (error) {
      console.error('Chat error:', error)
      onError(error as Error)
      
      // Add error message
      const errorMessage: ChatMessage = {
        id: `error_${Date.now()}`,
        content: 'Sorry, I encountered an error. Please try again.',
        isBot: true,
        timestamp: new Date(),
        error: (error as Error).message,
        feedback: null,
      }
      
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const streamChatResponse = async (content: string, userMessageId: string) => {
    const response = await fetch(`${config.apiUrl}agents/getEntityRecordAgent/stream`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Transfer-Encoding': 'chunked',
        'Connection': 'keep-alive',
      },
      body: JSON.stringify({
        messages: [{ role: 'user', content }],
        resourceId: config.user?.resourceId || 'default',
        threadId,
        token: config.token,
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('No response body reader available')
    }

    const decoder = new TextDecoder()
    let assistantMessage: ChatMessage = {
      id: `assistant_${Date.now()}`,
      content: '',
      isBot: true,
      timestamp: new Date(),
      feedback: null,
    }

    // Add empty assistant message that we'll update
    setMessages(prev => [...prev, assistantMessage])

    try {
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            
            if (data === '[DONE]') {
              break
            }

            try {
              const parsed = JSON.parse(data)
              
              if (parsed.content) {
                assistantMessage.content += parsed.content
                
                // Update the message in state
                setMessages(prev => 
                  prev.map(msg => 
                    msg.id === assistantMessage.id 
                      ? { ...assistantMessage }
                      : msg
                  )
                )
              }

              if (parsed.toolInvocations) {
                assistantMessage.toolInvocations = parsed.toolInvocations
              }
            } catch (parseError) {
              console.warn('Failed to parse streaming data:', parseError)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }

    // Notify parent component
    onMessage(assistantMessage)
  }

  const handleMessageFeedback = (messageId: string, feedback: 'like' | 'dislike') => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, feedback: msg.feedback === feedback ? null : feedback }
          : msg
      )
    )

    // If dislike, send follow-up message
    if (feedback === 'dislike') {
      handleSendMessage('Sorry this info is not correct, can you try again')
    }
  }

  const handleRetry = () => {
    // Find the last user message and retry it
    const lastUserMessage = [...messages].reverse().find(msg => !msg.isBot)
    if (lastUserMessage) {
      handleSendMessage(lastUserMessage.content)
    }
  }

  const handleNewChat = () => {
    // Reset messages to just the welcome message
    const welcomeMessage: ChatMessage = {
      id: 'welcome-1',
      content: `I'm **Luma** 👋\n\nI'm your AI assistant. I can help you with:\n\n- **Data analysis** and visualization\n- **Business insights** and recommendations\n- **Taking actions** based on your requests\n\nHow can I assist you today?`,
      isBot: true,
      timestamp: new Date(),
      feedback: null,
    }

    if (config.user?.firstName) {
      welcomeMessage.content = `Hi **${config.user.firstName}**, ${welcomeMessage.content}`
    }

    setMessages([welcomeMessage])
    
    // Generate new thread ID
    const newThreadId = `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    setThreadId(newThreadId)
  }

  return (
    <div className="ai-chat-widget-root">
      {config.showToggleButton && (
        <ToggleButton
          isOpen={isOpen}
          isLoading={isLoading}
          onClick={onToggle}
        />
      )}
      
      {isOpen && (
        <ChatWindow
          config={config}
          messages={messages}
          isLoading={isLoading}
          onSendMessage={handleSendMessage}
          onClose={onClose}
          onMessageFeedback={handleMessageFeedback}
          onRetry={handleRetry}
          onNewChat={handleNewChat}
        />
      )}
    </div>
  )
}
