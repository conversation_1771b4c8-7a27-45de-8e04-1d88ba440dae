/**
 * Messages List Component
 * 
 * Renders the list of chat messages with typing indicator
 */

import { MessageItem } from './MessageItem'
import { TypingIndicator } from './TypingIndicator'
import type { ChatMessage } from '../types'

interface MessagesListProps {
  messages: ChatMessage[]
  isLoading: boolean
  onFeedback: (messageId: string, feedback: 'like' | 'dislike') => void
  onRetry: () => void
  enableFeedback?: boolean
}

export function MessagesList({
  messages,
  isLoading,
  onFeedback,
  onRetry,
  enableFeedback = true,
}: MessagesListProps) {
  return (
    <>
      {messages.map((message) => (
        <MessageItem
          key={message.id}
          message={message}
          onFeedback={enableFeedback ? onFeedback : undefined}
          onRetry={message.error ? onRetry : undefined}
        />
      ))}
      
      {isLoading && <TypingIndicator />}
    </>
  )
}
