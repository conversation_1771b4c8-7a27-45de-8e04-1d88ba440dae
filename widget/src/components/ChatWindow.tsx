/**
 * Chat Window Component
 * 
 * Main chat interface with header, messages, and input
 */

import { useRef, useEffect } from 'preact/hooks'
import { ChatHeader } from './ChatHeader'
import { MessagesList } from './MessagesList'
import { ChatInput } from './ChatInput'
import type { WidgetConfig, ChatMessage } from '../types'

interface ChatWindowProps {
  config: WidgetConfig
  messages: ChatMessage[]
  isLoading: boolean
  onSendMessage: (content: string) => void
  onClose: () => void
  onMessageFeedback: (messageId: string, feedback: 'like' | 'dislike') => void
  onRetry: () => void
  onNewChat: () => void
}

export function ChatWindow({
  config,
  messages,
  isLoading,
  onSendMessage,
  onClose,
  onMessageFeedback,
  onRetry,
  onNewChat,
}: ChatWindowProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [messages])

  return (
    <div className={`ai-chat-widget-chat-window ${config.position === 'inline' ? 'inline' : 'open'}`}>
      <ChatHeader
        onClose={onClose}
        onNewChat={onNewChat}
        messagesCount={messages.length}
      />
      
      <div className="ai-chat-widget-messages">
        <MessagesList
          messages={messages}
          isLoading={isLoading}
          onFeedback={onMessageFeedback}
          onRetry={onRetry}
          enableFeedback={config.enableFeedback}
        />
        <div ref={messagesEndRef} />
      </div>
      
      <ChatInput
        onSendMessage={onSendMessage}
        isLoading={isLoading}
        placeholder="Type your message..."
      />
    </div>
  )
}
