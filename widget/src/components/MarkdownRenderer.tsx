/**
 * Markdown Renderer Component using Streamdown.ai
 * 
 * Renders markdown content safely with custom styling
 */

import { useEffect, useRef } from 'preact/hooks'

interface MarkdownRendererProps {
  content: string
}

export function MarkdownRenderer({ content }: MarkdownRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!containerRef.current) return

    // For now, we'll use a simple markdown-like renderer
    // In production, this would use Streamdown.ai
    const htmlContent = renderMarkdown(content)
    containerRef.current.innerHTML = htmlContent
  }, [content])

  return (
    <div 
      ref={containerRef}
      className="ai-chat-widget-markdown"
    />
  )
}

/**
 * Simple markdown renderer (placeholder for Streamdown.ai)
 * This is a basic implementation - in production you'd use Streamdown.ai
 */
function renderMarkdown(content: string): string {
  let html = content
  
  // Escape HTML first
  html = html
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
  
  // Bold text
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
  
  // Italic text
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>')
  
  // Code blocks
  html = html.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
  
  // Inline code
  html = html.replace(/`([^`]+)`/g, '<code>$1</code>')
  
  // Links
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')
  
  // Headers
  html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>')
  html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>')
  html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>')
  
  // Lists
  html = html.replace(/^\- (.*$)/gm, '<li>$1</li>')
  html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
  
  // Line breaks
  html = html.replace(/\n\n/g, '</p><p>')
  html = html.replace(/\n/g, '<br>')
  
  // Wrap in paragraphs
  if (!html.includes('<h1>') && !html.includes('<h2>') && !html.includes('<h3>') && !html.includes('<ul>')) {
    html = `<p>${html}</p>`
  }
  
  return html
}

// TODO: Replace with actual Streamdown.ai implementation
// Example of how it would look:
/*
import { streamdown } from 'streamdown'

export function MarkdownRenderer({ content }: MarkdownRendererProps) {
  const [html, setHtml] = useState('')

  useEffect(() => {
    const renderContent = async () => {
      try {
        const result = await streamdown(content, {
          // Streamdown.ai options
          sanitize: true,
          breaks: true,
          linkify: true,
        })
        setHtml(result)
      } catch (error) {
        console.error('Markdown rendering error:', error)
        setHtml(content) // Fallback to plain text
      }
    }

    renderContent()
  }, [content])

  return (
    <div 
      className="ai-chat-widget-markdown"
      dangerouslySetInnerHTML={{ __html: html }}
    />
  )
}
*/
