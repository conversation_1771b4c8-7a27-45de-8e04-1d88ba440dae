/**
 * AI Chat Widget - Embeddable Chat Interface
 * 
 * This is the main entry point for the embeddable AI chat widget.
 * It provides a simple API for integrating the chat widget into any website.
 */

import { AIChatWidget } from './widget/AIChatWidget'
import type { WidgetConfig, WidgetInstance } from './types'

// Global widget instances registry
const widgetInstances = new Map<string, WidgetInstance>()

/**
 * Initialize the AI Chat Widget
 * @param config - Widget configuration options
 * @returns Widget instance with control methods
 */
function init(config: WidgetConfig): WidgetInstance {
  const instanceId = config.containerId || `ai-chat-widget-${Date.now()}`
  
  // Check if widget already exists for this container
  if (widgetInstances.has(instanceId)) {
    console.warn(`AI Chat Widget already initialized for container: ${instanceId}`)
    return widgetInstances.get(instanceId)!
  }

  // Create new widget instance
  const widget = new AIChatWidget(config)
  const instance: WidgetInstance = {
    id: instanceId,
    widget,
    open: () => widget.open(),
    close: () => widget.close(),
    toggle: () => widget.toggle(),
    destroy: () => {
      widget.destroy()
      widgetInstances.delete(instanceId)
    },
    updateConfig: (newConfig: Partial<WidgetConfig>) => widget.updateConfig(newConfig),
    on: (event: string, callback: Function) => widget.on(event, callback),
    off: (event: string, callback: Function) => widget.off(event, callback),
  }

  widgetInstances.set(instanceId, instance)
  return instance
}

/**
 * Get existing widget instance by ID
 * @param instanceId - Widget instance ID
 * @returns Widget instance or undefined
 */
function getInstance(instanceId: string): WidgetInstance | undefined {
  return widgetInstances.get(instanceId)
}

/**
 * Destroy all widget instances
 */
function destroyAll(): void {
  widgetInstances.forEach(instance => instance.destroy())
  widgetInstances.clear()
}

// Global API
const AIChatWidgetAPI = {
  init,
  getInstance,
  destroyAll,
  version: '1.0.0',
}

// Export for module systems
export default AIChatWidgetAPI
export { init, getInstance, destroyAll }
export type { WidgetConfig, WidgetInstance }

// Global window assignment for script tag usage
if (typeof window !== 'undefined') {
  (window as any).AIChatWidget = AIChatWidgetAPI
}
