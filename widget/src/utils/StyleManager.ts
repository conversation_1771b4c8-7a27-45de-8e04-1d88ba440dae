/**
 * Style Manager for CSS generation and theming
 */

import type { WidgetTheme } from '../types'

export class StyleManager {
  private theme: WidgetTheme
  private cssPrefix = 'ai-chat-widget'

  constructor(theme: WidgetTheme = {}) {
    this.theme = this.normalizeTheme(theme)
  }

  /**
   * Update theme configuration
   */
  public updateTheme(newTheme: WidgetTheme = {}): void {
    this.theme = this.normalizeTheme({ ...this.theme, ...newTheme })
  }

  /**
   * Generate complete CSS styles for the widget
   */
  public getStyles(): string {
    return `
      ${this.getResetStyles()}
      ${this.getBaseStyles()}
      ${this.getComponentStyles()}
      ${this.getAnimationStyles()}
      ${this.getResponsiveStyles()}
    `
  }

  /**
   * Normalize theme with defaults
   */
  private normalizeTheme(theme: WidgetTheme): WidgetTheme {
    return {
      primaryColor: '#00BFE0',
      secondaryColor: '#1B617A',
      backgroundColor: '#ffffff',
      textColor: '#000000',
      borderRadius: '8px',
      fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: '14px',
      ...theme,
      header: {
        backgroundColor: theme.primaryColor || '#00BFE0',
        textColor: '#ffffff',
        ...theme.header,
      },
      toggleButton: {
        backgroundColor: theme.primaryColor || '#00BFE0',
        hoverColor: theme.secondaryColor || '#1B617A',
        size: '56px',
        ...theme.toggleButton,
      },
      messages: {
        userBubbleColor: theme.primaryColor || '#00BFE0',
        botBubbleColor: '#f1f3f4',
        userTextColor: '#ffffff',
        botTextColor: theme.textColor || '#000000',
        ...theme.messages,
      },
    }
  }

  /**
   * CSS Reset styles to prevent host site interference
   */
  private getResetStyles(): string {
    return `
      .${this.cssPrefix}-root {
        /* Reset all inherited styles */
        all: initial;
        
        /* Set base font properties */
        font-family: ${this.theme.fontFamily};
        font-size: ${this.theme.fontSize};
        line-height: 1.5;
        color: ${this.theme.textColor};
        
        /* Box model */
        box-sizing: border-box;
        
        /* Ensure proper stacking */
        position: relative;
        z-index: 1;
      }
      
      .${this.cssPrefix}-root *,
      .${this.cssPrefix}-root *::before,
      .${this.cssPrefix}-root *::after {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }
      
      .${this.cssPrefix}-root button {
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
        color: inherit;
        background: none;
        border: none;
        cursor: pointer;
      }
      
      .${this.cssPrefix}-root input,
      .${this.cssPrefix}-root textarea {
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
        color: inherit;
        background: none;
        border: none;
        outline: none;
      }
    `
  }

  /**
   * Base layout and positioning styles
   */
  private getBaseStyles(): string {
    return `
      .${this.cssPrefix}-container {
        position: fixed;
        z-index: 9999;
        pointer-events: none;
      }
      
      .${this.cssPrefix}-root {
        pointer-events: auto;
        font-family: ${this.theme.fontFamily};
        font-size: ${this.theme.fontSize};
        color: ${this.theme.textColor};
      }
      
      .${this.cssPrefix}-chat-window {
        position: relative;
        width: 380px;
        max-width: 90vw;
        height: 600px;
        max-height: 80vh;
        background: ${this.theme.backgroundColor};
        border-radius: ${this.theme.borderRadius};
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        transform-origin: bottom right;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
      
      .${this.cssPrefix}-chat-window.closed {
        transform: scale(0.8);
        opacity: 0;
        pointer-events: none;
      }
      
      .${this.cssPrefix}-chat-window.open {
        transform: scale(1);
        opacity: 1;
        pointer-events: auto;
      }
    `
  }

  /**
   * Component-specific styles
   */
  private getComponentStyles(): string {
    return `
      /* Toggle Button */
      .${this.cssPrefix}-toggle-button {
        width: ${this.theme.toggleButton?.size};
        height: ${this.theme.toggleButton?.size};
        border-radius: 50%;
        background: ${this.theme.toggleButton?.backgroundColor};
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }
      
      .${this.cssPrefix}-toggle-button:hover {
        background: ${this.theme.toggleButton?.hoverColor};
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
      }
      
      .${this.cssPrefix}-toggle-button:active {
        transform: scale(0.95);
      }
      
      /* Header */
      .${this.cssPrefix}-header {
        background: ${this.theme.header?.backgroundColor};
        color: ${this.theme.header?.textColor};
        padding: 16px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }
      
      .${this.cssPrefix}-header-title {
        font-weight: 600;
        font-size: 16px;
        margin: 0;
      }
      
      .${this.cssPrefix}-header-subtitle {
        font-size: 12px;
        opacity: 0.8;
        margin: 0;
      }
      
      .${this.cssPrefix}-close-button {
        background: none;
        border: none;
        color: inherit;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        opacity: 0.8;
        transition: opacity 0.2s ease;
      }
      
      .${this.cssPrefix}-close-button:hover {
        opacity: 1;
        background: rgba(255, 255, 255, 0.1);
      }
      
      /* Messages Area */
      .${this.cssPrefix}-messages {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 12px;
      }
      
      .${this.cssPrefix}-message {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        max-width: 85%;
      }
      
      .${this.cssPrefix}-message.user {
        flex-direction: row-reverse;
        align-self: flex-end;
      }
      
      .${this.cssPrefix}-message.bot {
        align-self: flex-start;
      }
      
      .${this.cssPrefix}-message-bubble {
        padding: 12px 16px;
        border-radius: ${this.theme.borderRadius};
        word-wrap: break-word;
        position: relative;
      }
      
      .${this.cssPrefix}-message-bubble.user {
        background: ${this.theme.messages?.userBubbleColor};
        color: ${this.theme.messages?.userTextColor};
        border-bottom-right-radius: 4px;
      }
      
      .${this.cssPrefix}-message-bubble.bot {
        background: ${this.theme.messages?.botBubbleColor};
        color: ${this.theme.messages?.botTextColor};
        border-bottom-left-radius: 4px;
      }
      
      .${this.cssPrefix}-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 600;
        flex-shrink: 0;
      }
      
      .${this.cssPrefix}-avatar.user {
        background: ${this.theme.messages?.userBubbleColor};
        color: ${this.theme.messages?.userTextColor};
      }
      
      .${this.cssPrefix}-avatar.bot {
        background: ${this.theme.primaryColor};
        color: white;
      }
      
      /* Input Area */
      .${this.cssPrefix}-input-area {
        padding: 16px 20px;
        border-top: 1px solid #e5e7eb;
        background: ${this.theme.backgroundColor};
      }
      
      .${this.cssPrefix}-input-container {
        display: flex;
        align-items: flex-end;
        gap: 8px;
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: ${this.theme.borderRadius};
        padding: 8px 12px;
        transition: border-color 0.2s ease;
      }
      
      .${this.cssPrefix}-input-container:focus-within {
        border-color: ${this.theme.primaryColor};
        box-shadow: 0 0 0 3px rgba(0, 191, 224, 0.1);
      }
      
      .${this.cssPrefix}-input {
        flex: 1;
        background: none;
        border: none;
        outline: none;
        resize: none;
        min-height: 20px;
        max-height: 100px;
        font-family: inherit;
        font-size: inherit;
        line-height: 1.5;
      }
      
      .${this.cssPrefix}-send-button {
        background: ${this.theme.primaryColor};
        color: white;
        border: none;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        flex-shrink: 0;
      }
      
      .${this.cssPrefix}-send-button:hover:not(:disabled) {
        background: ${this.theme.secondaryColor};
        transform: scale(1.05);
      }
      
      .${this.cssPrefix}-send-button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    `
  }

  /**
   * Animation styles
   */
  private getAnimationStyles(): string {
    return `
      @keyframes ${this.cssPrefix}-fade-in {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      @keyframes ${this.cssPrefix}-pulse {
        0%, 100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }
      
      @keyframes ${this.cssPrefix}-typing {
        0%, 60%, 100% {
          transform: translateY(0);
        }
        30% {
          transform: translateY(-10px);
        }
      }
      
      .${this.cssPrefix}-message {
        animation: ${this.cssPrefix}-fade-in 0.3s ease-out;
      }
      
      .${this.cssPrefix}-typing-indicator {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 12px 16px;
        background: ${this.theme.messages?.botBubbleColor};
        border-radius: ${this.theme.borderRadius};
        border-bottom-left-radius: 4px;
      }
      
      .${this.cssPrefix}-typing-dot {
        width: 6px;
        height: 6px;
        background: ${this.theme.secondaryColor};
        border-radius: 50%;
        animation: ${this.cssPrefix}-typing 1.4s infinite;
      }
      
      .${this.cssPrefix}-typing-dot:nth-child(2) {
        animation-delay: 0.2s;
      }
      
      .${this.cssPrefix}-typing-dot:nth-child(3) {
        animation-delay: 0.4s;
      }
    `
  }

  /**
   * Responsive styles
   */
  private getResponsiveStyles(): string {
    return `
      @media (max-width: 480px) {
        .${this.cssPrefix}-chat-window {
          width: 100vw;
          height: 100vh;
          max-width: 100vw;
          max-height: 100vh;
          border-radius: 0;
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
        }
        
        .${this.cssPrefix}-container {
          position: fixed !important;
          top: 0 !important;
          left: 0 !important;
          right: 0 !important;
          bottom: 0 !important;
          width: 100vw !important;
          height: 100vh !important;
        }
      }
    `
  }
}
