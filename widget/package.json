{"name": "ai-chat-widget", "version": "1.0.0", "description": "Embeddable AI Chat Widget", "main": "dist/ai-chat-widget.js", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit"}, "keywords": ["ai", "chat", "widget", "embeddable", "customer-support"], "author": "Your Company", "license": "MIT", "dependencies": {"preact": "^10.19.3"}, "devDependencies": {"@preact/preset-vite": "^2.7.0", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.16.0", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.32", "rollup-plugin-postcss": "^4.0.2", "terser": "^5.44.0", "typescript": "^5.3.3", "vite": "^5.0.10", "vite-plugin-css-injected-by-js": "^3.3.1", "vite-plugin-dts": "^3.7.0", "vitest": "^1.1.0"}}