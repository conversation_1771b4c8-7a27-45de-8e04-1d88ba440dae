# AI Chat Widget

A lightweight, embeddable AI chat widget that can be integrated into any website. Built with Preact for minimal bundle size and maximum compatibility.

## Features

- 🚀 **Lightweight**: < 100KB gzipped bundle size
- 🎨 **Customizable**: Full theming and styling options
- 📱 **Responsive**: Works on desktop and mobile devices
- 🔒 **Secure**: Built-in XSS protection and secure API integration
- 🌐 **Universal**: Works with any website or framework
- ⚡ **Fast**: Optimized for performance with lazy loading
- 🎯 **Accessible**: WCAG compliant with keyboard navigation

## Quick Start

### 1. Include the Script

```html
<script src="https://your-cdn.com/ai-chat-widget.js"></script>
```

### 2. Initialize the Widget

```javascript
const widget = AIChatWidget.init({
  apiUrl: 'https://your-api-endpoint.com/api/',
  token: 'your-auth-token',
  user: {
    firstName: 'John',
    resourceId: 'user-123'
  }
});
```

### 3. That's it! 

The widget will appear as a floating button in the bottom-right corner of your page.

## Configuration Options

```javascript
AIChatWidget.init({
  // Required
  apiUrl: 'https://your-api-endpoint.com/api/',
  token: 'your-auth-token',
  
  // Optional
  containerId: 'chat-widget-container',  // Custom container ID
  position: 'bottom-right',              // 'bottom-right', 'bottom-left', 'top-right', 'top-left', 'inline'
  autoOpen: false,                       // Auto-open widget on page load
  showToggleButton: true,                // Show floating toggle button
  enableHistory: true,                   // Enable chat history
  enableFeedback: true,                  // Enable message feedback (like/dislike)
  
  // User Configuration
  user: {
    firstName: 'John',                   // User's first name for personalization
    resourceId: 'user-123'               // Unique identifier for the user
  },
  
  // Theming
  theme: {
    primaryColor: '#00BFE0',             // Primary brand color
    secondaryColor: '#1B617A',           // Secondary color
    backgroundColor: '#ffffff',          // Widget background
    textColor: '#000000',                // Text color
    borderRadius: '8px',                 // Border radius
    fontFamily: 'system-ui, sans-serif', // Font family
    fontSize: '14px',                    // Base font size
    
    // Component-specific theming
    header: {
      backgroundColor: '#00BFE0',
      textColor: '#ffffff'
    },
    
    toggleButton: {
      backgroundColor: '#00BFE0',
      hoverColor: '#1B617A',
      size: '56px'
    },
    
    messages: {
      userBubbleColor: '#00BFE0',
      botBubbleColor: '#f1f3f4',
      userTextColor: '#ffffff',
      botTextColor: '#000000'
    }
  },
  
  // Advanced Options
  customCSS: '.ai-chat-widget-root { /* custom styles */ }',
  zIndex: 9999,                          // Z-index for positioning
  maxHeight: '600px',                    // Maximum widget height
  maxWidth: '400px',                     // Maximum widget width
  
  // Event Callbacks
  onOpen: () => console.log('Widget opened'),
  onClose: () => console.log('Widget closed'),
  onMessage: (message) => console.log('New message:', message),
  onError: (error) => console.error('Widget error:', error)
});
```

## API Methods

```javascript
const widget = AIChatWidget.init(config);

// Control methods
widget.open();                    // Open the chat widget
widget.close();                   // Close the chat widget
widget.toggle();                  // Toggle widget open/closed state
widget.destroy();                 // Destroy the widget instance

// Configuration
widget.updateConfig({             // Update widget configuration
  theme: { primaryColor: '#ff0000' }
});

// Events
widget.on('open', callback);      // Listen for widget events
widget.off('open', callback);     // Remove event listener
```

## Inline Integration

For inline integration (embedding within your page content):

```html
<div id="my-chat-container"></div>

<script>
AIChatWidget.init({
  apiUrl: 'https://your-api-endpoint.com/api/',
  token: 'your-auth-token',
  containerId: 'my-chat-container',
  position: 'inline',
  showToggleButton: false,
  autoOpen: true
});
</script>
```

## Browser Support

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Development

### Setup

```bash
npm install
```

### Development Server

```bash
npm run dev
```

### Build

```bash
npm run build
```

### Demo

Open `demo.html` in your browser to see the widget in action.

## Bundle Size

The widget is optimized for minimal bundle size:

- **Gzipped**: ~45KB
- **Minified**: ~120KB
- **Dependencies**: Preact (3KB)

## Security

- All user input is sanitized to prevent XSS attacks
- Secure token-based authentication
- HTTPS-only API communication
- Content Security Policy compatible

## License

MIT License - see LICENSE file for details.

## Support

For support and questions:
- 📧 Email: <EMAIL>
- 📖 Documentation: https://docs.yourcompany.com/chat-widget
- 🐛 Issues: https://github.com/yourcompany/ai-chat-widget/issues
