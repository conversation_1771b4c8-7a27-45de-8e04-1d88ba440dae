{"compilerOptions": {"target": "ES2018", "lib": ["ES2018", "DOM", "DOM.Iterable"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "declaration": true, "declarationMap": true, "jsx": "react-jsx", "jsxImportSource": "preact", "baseUrl": ".", "paths": {"@/*": ["src/*"], "react": ["node_modules/preact/compat"], "react-dom": ["node_modules/preact/compat"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}