<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Widget Demo</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }

        .demo-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .demo-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .demo-header h1 {
            font-size: 2.5rem;
            margin-bottom: 16px;
        }

        .demo-header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .demo-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 32px;
        }

        .demo-section {
            margin-bottom: 32px;
        }

        .demo-section h2 {
            font-size: 1.5rem;
            margin-bottom: 16px;
        }

        .demo-controls {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 24px;
        }

        .demo-button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .demo-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .demo-code {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 16px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 16px 0;
        }

        .status {
            padding: 12px;
            border-radius: 6px;
            margin: 16px 0;
            font-size: 14px;
        }

        .status.success {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .status.error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🤖 AI Chat Widget</h1>
            <p>Embeddable AI chat widget demo - Test the integration and features</p>
        </div>

        <div class="demo-content">
            <div class="demo-section">
                <h2>Widget Controls</h2>
                <div class="demo-controls">
                    <button class="demo-button" onclick="initWidget()">Initialize Widget</button>
                    <button class="demo-button" onclick="openWidget()">Open Chat</button>
                    <button class="demo-button" onclick="closeWidget()">Close Chat</button>
                    <button class="demo-button" onclick="toggleWidget()">Toggle Chat</button>
                    <button class="demo-button" onclick="destroyWidget()">Destroy Widget</button>
                </div>
                <div id="status" class="status" style="display: none;"></div>
            </div>

            <div class="demo-section">
                <h2>Integration Code</h2>
                <p>Copy this code to integrate the widget into your website:</p>
                <div class="demo-code">
&lt;script src="./dist/ai-chat-widget.umd.cjs"&gt;&lt;/script&gt;
&lt;script&gt;
  const widget = AIChatWidget.init({
    apiUrl: 'https://agent-server-mastra.thedma04.workers.dev/api/',
    token: 'your-token-here',
    user: {
      firstName: 'Demo User',
      resourceId: 'demo-resource-123'
    },
    theme: {
      primaryColor: '#00BFE0',
      secondaryColor: '#1B617A'
    },
    position: 'bottom-right',
    autoOpen: false
  });
&lt;/script&gt;
                </div>
            </div>

            <div class="demo-section">
                <h2>Configuration Options</h2>
                <div class="demo-code">
{
  // Required
  apiUrl: string,           // Your AI API endpoint
  token: string,            // Authentication token

  // Optional
  position: 'bottom-right', // Widget position
  autoOpen: false,          // Auto-open on load
  showToggleButton: true,   // Show floating button
  enableHistory: true,      // Enable chat history
  enableFeedback: true,     // Enable message feedback

  // User info
  user: {
    firstName: string,      // User's first name
    resourceId: string      // Unique user identifier
  },

  // Theming
  theme: {
    primaryColor: '#00BFE0',
    secondaryColor: '#1B617A',
    backgroundColor: '#ffffff',
    // ... more theme options
  }
}
                </div>
            </div>
        </div>
    </div>

    <!-- Widget will be loaded here -->
    <script src="./dist/ai-chat-widget.umd.cjs"></script>

    <script>
        let widgetInstance = null;

        function showStatus(message, type = 'success') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';

            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        function initWidget() {
            try {
                if (widgetInstance) {
                    widgetInstance.destroy();
                }

                widgetInstance = AIChatWidget.init({
                    apiUrl: 'https://agent-server-mastra.thedma04.workers.dev/api/',
                    token: 'demo-token-123',
                    user: {
                        firstName: 'Demo User',
                        resourceId: 'demo-resource-123'
                    },
                    theme: {
                        primaryColor: '#00BFE0',
                        secondaryColor: '#1B617A'
                    },
                    position: 'bottom-right',
                    autoOpen: false,
                    onOpen: () => console.log('Widget opened'),
                    onClose: () => console.log('Widget closed'),
                    onMessage: (message) => console.log('New message:', message),
                    onError: (error) => console.error('Widget error:', error)
                });

                showStatus('Widget initialized successfully!');
            } catch (error) {
                showStatus('Failed to initialize widget: ' + error.message, 'error');
            }
        }

        function openWidget() {
            if (widgetInstance) {
                widgetInstance.open();
                showStatus('Widget opened');
            } else {
                showStatus('Please initialize widget first', 'error');
            }
        }

        function closeWidget() {
            if (widgetInstance) {
                widgetInstance.close();
                showStatus('Widget closed');
            } else {
                showStatus('Please initialize widget first', 'error');
            }
        }

        function toggleWidget() {
            if (widgetInstance) {
                widgetInstance.toggle();
                showStatus('Widget toggled');
            } else {
                showStatus('Please initialize widget first', 'error');
            }
        }

        function destroyWidget() {
            if (widgetInstance) {
                widgetInstance.destroy();
                widgetInstance = null;
                showStatus('Widget destroyed');
            } else {
                showStatus('No widget to destroy', 'error');
            }
        }

        // Auto-initialize for demo
        window.addEventListener('load', () => {
            setTimeout(initWidget, 1000);
        });
    </script>
</body>
</html>
