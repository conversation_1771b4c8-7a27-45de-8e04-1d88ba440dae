<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Widget Test</title>
    <style>
        body {
            font-family: system-ui, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-content {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 16px;
        }
        .status {
            padding: 12px;
            border-radius: 6px;
            margin: 20px 0;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        .btn-primary {
            background: #00BFE0;
            color: white;
        }
        .btn-primary:hover {
            background: #1B617A;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="test-content">
        <h1>🤖 AI Chat Widget Test</h1>
        <p>This is a simple test page to verify the AI chat widget is working correctly.</p>
        
        <div id="status" class="status">
            <span id="status-text">Loading widget...</span>
        </div>
        
        <div class="controls">
            <button id="open-btn" class="btn-primary">Open Chat</button>
            <button id="close-btn" class="btn-secondary">Close Chat</button>
            <button id="toggle-btn" class="btn-primary">Toggle Chat</button>
        </div>
        
        <h2>Instructions:</h2>
        <ol>
            <li>The chat widget should appear as a floating button in the bottom-right corner</li>
            <li>Click the floating button or "Open Chat" to open the chat window</li>
            <li>You should see a welcome message from Luma</li>
            <li>Try typing a message and sending it</li>
            <li>The widget should handle the conversation properly</li>
        </ol>
        
        <h2>Expected Features:</h2>
        <ul>
            <li>✅ Floating toggle button</li>
            <li>✅ Chat window with header</li>
            <li>✅ Welcome message</li>
            <li>✅ Message input and send functionality</li>
            <li>✅ Responsive design</li>
            <li>✅ Proper CSS isolation</li>
        </ul>
    </div>

    <!-- Load the widget -->
    <script src="./dist/ai-chat-widget.umd.cjs"></script>
    
    <script>
        let widget = null;
        
        // Initialize the widget
        try {
            widget = AIChatWidget.init({
                apiUrl: 'https://agent-server-mastra.thedma04.workers.dev/api/',
                token: 'demo-token-123',
                user: {
                    firstName: 'Test User',
                    resourceId: 'test-resource-123'
                },
                theme: {
                    primaryColor: '#00BFE0',
                    secondaryColor: '#1B617A'
                },
                onOpen: () => {
                    console.log('Widget opened');
                    updateStatus('Chat opened', 'success');
                },
                onClose: () => {
                    console.log('Widget closed');
                    updateStatus('Chat closed', 'success');
                },
                onMessage: (message) => {
                    console.log('New message:', message);
                },
                onError: (error) => {
                    console.error('Widget error:', error);
                    updateStatus(`Error: ${error.message}`, 'error');
                }
            });
            
            updateStatus('Widget loaded successfully! Look for the chat button in the bottom-right corner.', 'success');
            
        } catch (error) {
            console.error('Failed to initialize widget:', error);
            updateStatus(`Failed to load widget: ${error.message}`, 'error');
        }
        
        // Control buttons
        document.getElementById('open-btn').addEventListener('click', () => {
            if (widget) {
                widget.open();
            }
        });
        
        document.getElementById('close-btn').addEventListener('click', () => {
            if (widget) {
                widget.close();
            }
        });
        
        document.getElementById('toggle-btn').addEventListener('click', () => {
            if (widget) {
                widget.toggle();
            }
        });
        
        function updateStatus(message, type = 'success') {
            const statusEl = document.getElementById('status');
            const statusText = document.getElementById('status-text');
            
            statusText.textContent = message;
            statusEl.className = `status ${type}`;
        }
    </script>
</body>
</html>
