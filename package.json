{"name": "upace-dashboard", "private": true, "version": "1.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format:check": "prettier --check .", "format": "prettier --write .", "knip": "knip"}, "dependencies": {"@ai-sdk/react": "^1.2.8", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.4", "@radix-ui/react-visually-hidden": "^1.1.0", "@tabler/icons-react": "^3.24.0", "@tanstack/react-query": "^5.62.3", "@tanstack/react-router": "^1.86.1", "@tanstack/react-table": "^8.20.5", "axios": "^1.8.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^4.1.0", "js-cookie": "^3.0.5", "ky": "^1.8.0", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.0", "react-markdown": "^10.1.0", "recharts": "^2.14.1", "remark-gfm": "^4.0.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.16.0", "@faker-js/faker": "^9.3.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/eslint-plugin-query": "^5.62.1", "@tanstack/react-query-devtools": "^5.62.3", "@tanstack/router-devtools": "^1.86.1", "@tanstack/router-plugin": "^1.86.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/node": "^22.10.1", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "@vitejs/plugin-react-swc": "^3.7.2", "autoprefixer": "^10.4.20", "eslint": "^9.16.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.13.0", "knip": "^5.41.1", "postcss": "^8.4.49", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.16", "typescript": "~5.7.2", "typescript-eslint": "^8.17.0", "vite": "^6.0.9"}}