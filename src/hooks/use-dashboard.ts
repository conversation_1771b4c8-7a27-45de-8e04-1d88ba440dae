import {
  endOfDay,
  endOfMonth,
  format,
  parseISO,
  startOfDay,
  startOfMonth,
} from 'date-fns'
import { useQuery } from '@tanstack/react-query'
import { api } from '@/api/api'

export interface EntityPerformance {
  id: number
  name: string
  reservation_count: number
  custom_identifier?: string
}

export interface EntityData {
  id: number
  name: string
  days: string
  is_sgt: number
  start_time: string
  end_time: string
  start_date: string
  end_date: string
  gym_name: string
  instructor_first_name: string
  instructor_last_name: string
  custom_identifier?: string
}

export interface ReservationAttendance {
  date: string
  user_reservations_count: number
  custom_identifier?: string
}

export interface MemberRatio {
  distinct_member_reservation_count: number
  percentage: number
  ratio: number
  total_members: number
}
export interface ApiResponse {
  reservation_count: number
  custom_identifier: string
  key: string
  success: boolean
  data:
    | EntityPerformance[]
    | EntityData[]
    | number
    | ReservationAttendance[]
    | MemberRatio
}

// Transformed Types for UI
export interface DashboardStats {
  totalReservations: number
  totalClassCount: number
  totalCheckInCount: number
  memberRatio: number
}

export interface MonthlyReservationData {
  name: string // Month name
  total: number
}

export interface RecentActivity {
  year?: number
  id: number
  name: string
  instructor: string
  count: number
  gym: string
}

export interface DashboardData {
  stats: DashboardStats
  recentActivities: RecentActivity[]
  monthlyReservations: MonthlyReservationData[]
}

const startDate = parseISO(startOfDay(new Date()).toISOString())
const endDate = parseISO(endOfDay(new Date()).toISOString())

const getDefaultFields = (year: number = new Date().getFullYear()) => {
  const yearStart = new Date(year, 0, 1)
  const yearEnd = new Date(year, 11, 31)

  const yearStartStr = format(yearStart, 'yyyy-MM-dd')
  const yearEndStr = format(yearEnd, 'yyyy-MM-dd')

  return {
    agents: [
      {
        key: 'entity_performance',
        params: {
          entity_type: 'class',
          start_date: yearStartStr,
          end_date: yearEndStr,
        },
      },
      {
        key: 'entity_data',
        params: {
          entity_type: 'class',
        },
      },
      {
        key: 'total_classes_count',
        params: {
          date: startDate,
        },
      },
      {
        key: 'reservation_count',
        custom_identifier: 'reservation_count',
        params: {
          entity_type: 'class',
          start_date: startDate,
          end_date: endDate,
        },
      },
      {
        key: 'reservation_count',
        custom_identifier: 'reservation_checked_in',
        params: {
          entity_type: 'class',
          start_date: startDate,
          end_date: endDate,
          reservation_state: 'checked_in',
        },
      },
      {
        key: 'reservation_to_member_ratio',
        params: {
          entity_type: 'class',
          start_date: startOfMonth(new Date()),
          end_date: endOfMonth(new Date()),
        },
      },
      {
        key: 'reservation_attendance_series',
        params: {
          entity_type: 'class',
          start_date: yearStartStr,
          end_date: yearEndStr,
        },
      },
    ],
  }
}

export const fetchDashboardData = async (
  year: number = new Date().getFullYear()
): Promise<unknown[]> => {
  try {
    const response = await api
      .post<{ success?: boolean; data: unknown[] }>('congitive', {
        json: {
          ...getDefaultFields(year),
        },
      })
      .json()

    return response.data
  } catch (error) {
    // Instead of console.error, we'll throw the error to be handled by React Query
    throw new Error(
      `Failed to fetch dashboard data: ${error instanceof Error ? error.message : String(error)}`
    )
  }
}

function transformApiResponse(apiResponse: ApiResponse[]): DashboardData {
  const extractedData = apiResponse.reduce(
    (acc, item) => {
      switch (item.key) {
        case 'entity_performance':
          return { ...acc, entityPerformance: item.data as EntityPerformance[] }
        case 'entity_data':
          return { ...acc, entityData: item.data as EntityData[] }
        case 'total_classes_count':
          return { ...acc, totalClassesCount: item.data as number }
        case 'reservation_count':
          if (item.custom_identifier === 'reservation_count') {
            return {
              ...acc,
              totalReservations: item?.reservation_count as number,
            }
          } else if (item.custom_identifier === 'reservation_checked_in') {
            return {
              ...acc,
              totalCheckInCount: item?.reservation_count as number,
            }
          }
          return acc
        case 'reservation_attendance_series':
          return {
            ...acc,
            reservationAttendance: item.data as ReservationAttendance[],
          }
        case 'reservation_to_member_ratio':
          return {
            ...acc,
            memberRatio:
              typeof item.data === 'object' && 'percentage' in item.data
                ? (item.data as MemberRatio).percentage
                : 0,
          }
        default:
          return acc
      }
    },
    {
      entityPerformance: [] as EntityPerformance[],
      entityData: [] as EntityData[],
      totalReservations: 0,
      totalClassesCount: 0,
      totalCheckInCount: 0,
      memberRatio: 0,
      reservationAttendance: [] as ReservationAttendance[],
    }
  )

  // Process performance data
  const processRecentActivities = (
    performanceData: EntityPerformance[],
    entityData: EntityData[]
  ) => {
    // Sort by reservation count
    const sortedClasses = [...performanceData].sort(
      (a, b) => b.reservation_count - a.reservation_count
    )

    return sortedClasses.slice(0, 5).map((performance) => {
      const classData = entityData.find((e) => e.id === performance.id)
      return {
        id: performance.id,
        name: performance.name,
        instructor: classData
          ? `${classData.instructor_first_name} ${classData.instructor_last_name}`
          : 'Unknown Instructor',
        count: performance.reservation_count,
        gym: classData?.gym_name || 'Unknown Location',
      }
    })
  }

  // Process attendance data
  const processMonthlyReservations = (
    attendanceData: ReservationAttendance[]
  ) => {
    // Group by month for chart data
    const monthlyData = attendanceData.reduce(
      (acc, item) => {
        const month = format(new Date(item.date), 'MMM')
        return {
          ...acc,
          [month]: (acc[month] || 0) + item.user_reservations_count,
        }
      },
      {} as Record<string, number>
    )

    return Object.entries(monthlyData).map(([name, total]) => ({ name, total }))
  }

  // Process all data
  const recentActivities = processRecentActivities(
    extractedData.entityPerformance,
    extractedData.entityData
  )

  const monthlyReservations = processMonthlyReservations(
    extractedData.reservationAttendance
  )

  return {
    stats: {
      totalReservations: extractedData.totalReservations,
      totalClassCount: extractedData.totalClassesCount,
      totalCheckInCount: extractedData.totalCheckInCount,
      memberRatio: extractedData.memberRatio,
    },
    monthlyReservations,
    recentActivities,
  }
}

/**
 * Hook to fetch and manage dashboard data
 * @param year The year to fetch data for
 * @returns Query result with dashboard data
 */
export function useDashboard(year: number = new Date().getFullYear()) {
  return useQuery({
    queryKey: ['dashboard', year],
    queryFn: () => fetchDashboardData(year),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true,
    select: (data) => transformApiResponse(data as ApiResponse[]),
  })
}
