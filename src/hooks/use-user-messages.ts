import { useQuery } from '@tanstack/react-query'
import { aiApi } from '@/api/ai-api'

export type ThreadMessage = {
  id: string
  threadId: string
  resourceId: string
  role: 'user' | 'assistant'
  content: string
  createdAt: string
  type: string
}

export type Thread = {
  id: string
  title: string
  createdAt: string
  updatedAt: string
  resourceId: string
  metadata?: {
    workingMemory?: string
  }
  messages?: ThreadMessage[]
  messageCount?: number
}

type UserMessages = Thread[]

export const fetchMessages = async (resourceId?: string) => {
  try {
    return await aiApi
      .get<UserMessages>(
        `memory/threads?resourceid=${resourceId}&agentId=getEntityRecordAgent`
      )
      .json()
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error fetching messages:', error)
  }
}

export const useUserMessages = (resourceId?: string) => {
  return useQuery({
    queryKey: ['user-messages', resourceId],
    queryFn: () => fetchMessages(resourceId),
    refetchOnWindowFocus: true,
    refetchIntervalInBackground: true,
    select: (data) => data?.reverse(),
    refetchInterval: 50000,
  })
}
