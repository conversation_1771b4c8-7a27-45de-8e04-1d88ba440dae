import { useQuery } from '@tanstack/react-query'
import { api } from '@/api/api'

export const fetchClientInfo = async () => {
  const response = await api
    .get<{
      success: boolean
      data: Record<string, unknown | string>
    }>('auth/retrieve')
    .json()

  return response?.data
}

export function useClientInfo(token: string) {
  return useQuery({
    queryKey: ['clientInfo', token],
    queryFn: () => fetchClientInfo(),
    staleTime: Infinity,
  })
}
