import { useQuery } from '@tanstack/react-query'
import { aiApi } from '@/api/ai-api'

export type MessageContent = {
  type: 'text'
  text: string
}

export type ThreadMessage = {
  id: string
  threadId: string
  resourceId: string
  role: 'user' | 'assistant'
  content: MessageContent[]
  createdAt: string
  type: string
  toolCallIds?: string[]
  toolCallArgs?: Array<Record<string, unknown>>
  toolNames?: string[]
}

export type UIMessage = {
  id: string
  role: 'user' | 'assistant'
  content: string
  toolInvocations: unknown[]
  createdAt: string
}

export type ThreadMessagesResponse = {
  messages: ThreadMessage[]
  uiMessages: UIMessage[]
}

export const fetchThreadMessages = async (threadId: string) => {
  try {
    return await aiApi
      .get<ThreadMessagesResponse>(
        `memory/threads/${threadId}/messages?agentId=getEntityRecordAgent`
      )
      .json()
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error fetching thread messages:', error)
    throw error
  }
}

export const useThreadMessages = (threadId: string) => {
  return useQuery({
    queryKey: ['thread-messages', threadId],
    queryFn: () => fetchThreadMessages(threadId),
    enabled: !!threadId,
    refetchOnWindowFocus: true,
    staleTime: 30000, // 30 seconds
  })
}
