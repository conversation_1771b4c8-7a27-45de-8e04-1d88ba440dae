import { MonthlyReservationData, RecentActivity } from '@/hooks/use-dashboard'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { MonthlyChart } from './monthly-chart'
import { TopClassesList } from './top-classes-list'

interface DashboardChartsProps {
  monthlyReservations: MonthlyReservationData[]
  recentActivities: RecentActivity[]
  selectedYear: number
}

export function DashboardCharts({
  monthlyReservations,
  recentActivities,
  selectedYear,
}: DashboardChartsProps) {
  return (
    <div className='grid grid-cols-1 gap-4 lg:grid-cols-7'>
      <Card className='col-span-1 lg:col-span-4'>
        <CardHeader>
          <CardTitle>Monthly Reservations {selectedYear}</CardTitle>
          <CardDescription>
            Reservation counts by month for {selectedYear}
          </CardDescription>
        </CardHeader>
        <CardContent className='pl-2'>
          <MonthlyChart data={monthlyReservations} />
        </CardContent>
      </Card>
      <Card className='col-span-1 lg:col-span-3'>
        <CardHeader>
          <CardTitle>Top Classes {selectedYear}</CardTitle>
          <CardDescription>
            Classes with the most reservations in {selectedYear}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <TopClassesList activities={recentActivities} />
        </CardContent>
      </Card>
    </div>
  )
}
