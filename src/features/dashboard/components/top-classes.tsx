import { RecentActivity } from '@/hooks/use-dashboard'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'

interface RecentSalesProps {
  activities: RecentActivity[]
}

export function RecentSales({ activities }: RecentSalesProps) {
  return (
    <div className='space-y-8'>
      {activities.map((activity) => (
        <div key={activity.id} className='flex items-center gap-4'>
          <Avatar className='h-9 w-9'>
            <AvatarFallback>
              {activity.instructor
                .split(' ')
                .map((name) => name[0])
                .join('')}
            </AvatarFallback>
          </Avatar>
          <div className='flex flex-1 flex-wrap items-center justify-between'>
            <div className='space-y-1'>
              <p
                className='max-w-[180px] truncate text-sm font-medium leading-none'
                title={activity.name}
              >
                {activity.name}
              </p>
              <p className='text-sm text-muted-foreground'>
                {activity.instructor}
              </p>
            </div>
            <div className='font-medium'>{activity.count} reservations</div>
          </div>
        </div>
      ))}
    </div>
  )
}
