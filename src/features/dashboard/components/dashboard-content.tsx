import { useState } from 'react'
import { useDashboard } from '@/hooks/use-dashboard'
import { DashboardCharts } from './dashboard-charts'
import { DashboardError } from './dashboard-error'
import { DashboardHeader } from './dashboard-header'
import { DashboardSkeleton } from './dashboard-skeleton'
import { DashboardStats } from './dashboard-stats'

export function DashboardContent() {
  const [selectedYear, setSelectedYear] = useState<number>(() =>
    new Date().getFullYear()
  )

  const { data, isLoading, error } = useDashboard(selectedYear)

  const handleYearChange = (year: number) => setSelectedYear(year)

  if (error) {
    return <DashboardError />
  }

  if (isLoading) {
    return <DashboardSkeleton />
  }

  if (!data) {
    return null
  }

  return (
    <>
      <DashboardStats stats={data.stats} />
      <DashboardHeader
        selectedYear={selectedYear}
        onYearChange={handleYearChange}
      />
      <DashboardCharts
        monthlyReservations={data.monthlyReservations}
        recentActivities={data.recentActivities}
        selectedYear={selectedYear}
      />
    </>
  )
}
