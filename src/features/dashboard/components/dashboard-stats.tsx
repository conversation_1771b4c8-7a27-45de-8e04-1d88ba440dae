import { CalendarDays, CalendarCheck, Users, CheckSquare } from 'lucide-react'
import { DashboardStats as DashboardStatsType } from '@/hooks/use-dashboard'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface DashboardStatsProps {
  stats: DashboardStatsType
}

export function DashboardStats({ stats }: DashboardStatsProps) {
  return (
    <div className='grid gap-4 sm:grid-cols-2 lg:grid-cols-4'>
      <Card className='transition-shadow hover:shadow-md'>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>
            Number of Classes Today
          </CardTitle>
          <div className='flex h-9 w-9 items-center justify-center rounded-full bg-primary/10 transition-all hover:scale-110 hover:bg-primary/20'>
            <CalendarDays className='h-5 w-5 text-primary' color='#00BFE0' />
          </div>
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold text-black'>
            {stats.totalClassCount}
          </div>
        </CardContent>
      </Card>
      <Card className='transition-shadow hover:shadow-md'>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>
            Total Reservations Today
          </CardTitle>
          <div className='flex h-9 w-9 items-center justify-center rounded-full bg-primary/10 transition-all hover:scale-110 hover:bg-primary/20'>
            <CalendarCheck className='h-5 w-5 text-primary' color='#00BFE0' />
          </div>
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold text-black'>
            {stats.totalReservations ?? 0}
          </div>
        </CardContent>
      </Card>
      <Card className='transition-shadow hover:shadow-md'>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>
            Monthly Member Ratio
          </CardTitle>
          <div className='flex h-9 w-9 items-center justify-center rounded-full bg-primary/10 transition-all hover:scale-110 hover:bg-primary/20'>
            <Users className='h-5 w-5 text-primary' color='#00BFE0' />
          </div>
        </CardHeader>
        <CardContent>
          <div
            className='truncate text-2xl font-bold text-black'
            title={'stats.topClassName'}
          >
            {stats.memberRatio ?? 0}%
          </div>
        </CardContent>
      </Card>
      <Card className='transition-shadow hover:shadow-md'>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>
            Total Check-in Today
          </CardTitle>
          <div className='flex h-9 w-9 items-center justify-center rounded-full bg-primary/10 transition-all hover:scale-110 hover:bg-primary/20'>
            <CheckSquare className='h-5 w-5 text-primary' color='#00BFE0' />
          </div>
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold text-black'>
            {stats.totalCheckInCount ?? 0}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
