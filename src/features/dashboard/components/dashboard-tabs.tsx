import { ReactNode } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'

interface DashboardTabsProps {
  children: ReactNode
  sideNav?: ReactNode
}

// #1B617A
// #41C5E60

export function DashboardTabs({ children, sideNav }: DashboardTabsProps) {
  return (
    <Tabs orientation='vertical' defaultValue='classes' className='space-y-4'>
      <div className='flex items-center justify-between'>
        <div className='w-full overflow-x-auto pb-2'>
          <TabsList>
            <TabsTrigger disabled value='overview'>
              Overview
            </TabsTrigger>
            <TabsTrigger value='classes'>Classes</TabsTrigger>
            <TabsTrigger value='equipments' disabled>
              Equipments
            </TabsTrigger>
            <TabsTrigger value='personal-training' disabled>
              Personal Training
            </TabsTrigger>
          </TabsList>
        </div>
        {sideNav}
      </div>

      <TabsContent value='classes' className='space-y-4'>
        {children}
      </TabsContent>
    </Tabs>
  )
}
