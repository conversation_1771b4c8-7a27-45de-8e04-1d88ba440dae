import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
} from 'recharts'
import type { TooltipProps } from 'recharts'
import { MonthlyReservationData } from '@/hooks/use-dashboard'

interface MonthlyChartProps {
  data: MonthlyReservationData[]
}

const CustomTooltip = ({
  active,
  payload,
  label,
}: TooltipProps<number, string>) => {
  if (active && payload && payload.length) {
    return (
      <div className='rounded-md border border-border bg-background p-2 text-[#00BFE0] shadow-md'>
        <p className='text-sm font-medium'>{`${label}`}</p>
        <p className='text-sm font-semibold'>{`Reservations: ${payload[0].value}`}</p>
      </div>
    )
  }
  return null
}

export function MonthlyChart({ data }: MonthlyChartProps) {
  return (
    <ResponsiveContainer width='100%' height={350}>
      <BarChart data={data} margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
        <CartesianGrid strokeDasharray='3 3' vertical={false} opacity={0.3} />
        <XAxis
          dataKey='name'
          stroke='black'
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        <YAxis stroke='black' fontSize={12} tickLine={false} axisLine={false} />
        <Tooltip content={<CustomTooltip />} cursor={{ fill: 'white' }} />
        <Bar
          dataKey='total'
          fill='#00BFE0'
          color='#00BFE0'
          radius={[4, 4, 0, 0]}
          name='Reservations'
        />
      </BarChart>
    </ResponsiveContainer>
  )
}
