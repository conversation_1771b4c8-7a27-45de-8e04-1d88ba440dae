import { CalendarIcon } from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface YearFilterProps {
  selectedYear: number
  onYearChange: (year: number) => void
}

export function YearFilter({ selectedYear, onYearChange }: YearFilterProps) {
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 6 }, (_, i) => currentYear - i)

  return (
    <div className='flex w-[230px] items-center gap-2'>
      <span className='text-sm font-medium'>Filter By Year:</span>
      <Select
        value={selectedYear.toString()}
        onValueChange={(value) => onYearChange(parseInt(value))}
      >
        <SelectTrigger className='h-9 w-[120px] border-primary/20 bg-primary/5 pl-3 pr-2'>
          <div className='flex items-center gap-2'>
            <CalendarIcon className='h-4 w-4 text-primary/70' color='#00BFE0' />
            <SelectValue placeholder={selectedYear.toString()} />
          </div>
        </SelectTrigger>
        <SelectContent>
          {years.map((year) => (
            <SelectItem
              key={year}
              value={year.toString()}
              className={
                year === selectedYear ? 'bg-primary/10 font-medium' : ''
              }
            >
              {year}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}
