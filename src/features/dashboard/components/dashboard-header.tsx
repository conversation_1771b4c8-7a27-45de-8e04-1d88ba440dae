import { Header } from '@/components/layout/header'
import { YearFilter } from './year-filter'

interface DashboardHeaderProps {
  selectedYear: number
  onYearChange: (year: number) => void
}

export const DashboardHeader = ({
  selectedYear,
  onYearChange,
}: DashboardHeaderProps) => {
  return (
    <Header>
      <div className='ml-auto flex items-center space-x-4'>
        <YearFilter selectedYear={selectedYear} onYearChange={onYearChange} />
      </div>
    </Header>
  )
}
