import { RecentActivity } from '@/hooks/use-dashboard'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

interface TopClassesListProps {
  activities: RecentActivity[]
}

export function TopClassesList({ activities }: TopClassesListProps) {
  return (
    <TooltipProvider>
      <div className='space-y-8'>
        {activities.map((activity) => (
          <Tooltip key={activity.id}>
            <TooltipTrigger asChild>
              <div className='flex cursor-pointer items-center gap-4 rounded-md p-2 text-black transition-colors hover:bg-accent/50'>
                <Avatar className='h-9 w-9'>
                  <AvatarFallback>
                    {activity.instructor
                      .split(' ')
                      .map((name) => name[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>
                <div className='flex flex-1 flex-wrap items-center justify-between'>
                  <div className='space-y-1'>
                    <p className='max-w-[180px] truncate text-sm font-medium leading-none'>
                      {activity.name}
                    </p>
                    <p className='text-sm text-muted-foreground'>
                      {activity.instructor}
                    </p>
                  </div>
                  <div className='font-medium'>
                    {activity.count} reservations
                  </div>
                </div>
              </div>
            </TooltipTrigger>
            <TooltipContent className='max-w-xs bg-[#00BFE0]'>
              <div className='space-y-2 text-white'>
                <p className='font-semibold'>{activity.name}</p>
                <div className='grid grid-cols-2 gap-x-4 gap-y-1 text-sm'>
                  <span>Instructor:</span>
                  <span>{activity.instructor}</span>
                  <span>Location:</span>
                  <span>{activity.gym}</span>
                  <span>Reservations:</span>
                  <span>{activity.count}</span>
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        ))}
      </div>
    </TooltipProvider>
  )
}
