import { useEffect } from 'react'
import {
  createFileRoute,
  Outlet,
  useNavigate,
  useRouterState,
} from '@tanstack/react-router'
import { cn } from '@/lib/utils'

export const Route = createFileRoute('/_authenticated')({
  component: RouteComponent,
})

function RouteComponent() {
  const navigate = useNavigate()
  const routerState = useRouterState()

  // Redirect to home page if trying to access any route other than home
  useEffect(() => {
    const currentPath = routerState.location.pathname
    if (currentPath !== '/') {
      navigate({ to: '/' })
    }
  }, [routerState.location.pathname, navigate])

  return (
    <>
      <div
        id='content'
        className={cn(
          'w-full max-w-full',
          'flex h-svh flex-col',
          'group-data-[scroll-locked=1]/body:h-full',
          'group-data-[scroll-locked=1]/body:has-[main.fixed-main]:h-svh'
        )}
      >
        <Outlet />
      </div>
    </>
  )
}
