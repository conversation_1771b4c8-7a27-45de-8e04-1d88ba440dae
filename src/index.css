@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --secondary: 199 89% 48%;
    --secondary-foreground: 210 40% 98%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 221 83% 90%;
    --accent-foreground: 221 83% 30%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221 83% 53%;
    --chart-1: 221 83% 53%;
    --chart-2: 199 89% 48%;
    --chart-3: 262 83% 58%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 221 83% 98%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 221 83% 53%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 221 83% 94%;
    --sidebar-accent-foreground: 221 83% 30%;
    --sidebar-border: 221 83% 90%;
    --sidebar-ring: 221 83% 53%;
  }
  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;
    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;
    --popover: 224 71% 4%;
    --popover-foreground: 213 31% 91%;
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --secondary: 199 89% 48%;
    --secondary-foreground: 210 40% 98%;
    --muted: 223 47% 11%;
    --muted-foreground: 215 20.2% 75%;
    --accent: 221 83% 30%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 216 34% 17%;
    --input: 216 34% 17%;
    --ring: 221 83% 53%;
    --chart-1: 221 83% 65%;
    --chart-2: 199 89% 60%;
    --chart-3: 262 83% 70%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: var(--background);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--accent);
    --sidebar-accent-foreground: var(--accent-foreground);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);
  }

  /* styles.css */
  .CollapsibleContent {
    overflow: hidden;
  }
  .CollapsibleContent[data-state='open'] {
    animation: slideDown 300ms ease-out;
  }
  .CollapsibleContent[data-state='closed'] {
    animation: slideUp 300ms ease-out;
  }

  @keyframes slideDown {
    from {
      height: 0;
    }
    to {
      height: var(--radix-collapsible-content-height);
    }
  }

  @keyframes slideUp {
    from {
      height: var(--radix-collapsible-content-height);
    }
    to {
      height: 0;
    }
  }

  /* Prevent focus zoom on mobile devices */
  @media screen and (max-width: 767px) {
    input,
    select,
    textarea {
      font-size: 16px !important;
    }
  }
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .faded-bottom {
    @apply after:pointer-events-none after:absolute after:bottom-0 after:left-0 after:hidden after:h-32 after:w-full after:bg-[linear-gradient(180deg,_transparent_10%,_hsl(var(--background))_70%)] after:md:block;
  }
}

@layer base {
  * {
    @apply border-border;
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--border)) transparent;
  }
  html {
    @apply overflow-x-hidden;
  }
  body {
    @apply min-h-svh w-full bg-background text-foreground;
  }

  /* Add subtle gradient to dark mode background */
  .dark body {
    background: radial-gradient(
      circle at top right,
      hsl(var(--background)),
      hsl(224, 71%, 2%)
    );
  }
}
