import { useClientInfo } from '@/hooks/use-client-info'
import AIChatbot from './ai-chatbot/index'
import { token } from './ai-chatbot/token'

export const AIChatBot = () => {
  const { data, isPending } = useClientInfo(token)

  if (isPending) return <div>Loading...</div>

  if (!data) return <div>User not found</div>

  return (
    <AIChatbot
      resourceId={data?.id as string}
      firstName={data?.first_name as string}
    />
  )
}
