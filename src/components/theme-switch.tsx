import { useEffect } from 'react'
import { IconMoon, IconSun } from '@tabler/icons-react'
import { useTheme } from '@/context/theme-context'
import { Button } from '@/components/ui/button'

export function ThemeSwitch() {
  const { theme, setTheme } = useTheme()

  /* Update theme-color meta tag
   * when theme is updated */
  useEffect(() => {
    const themeColor = theme === 'dark' ? '#020817' : '#fff'
    const metaThemeColor = document.querySelector("meta[name='theme-color']")
    if (metaThemeColor) metaThemeColor.setAttribute('content', themeColor)
  }, [theme])

  // Toggle between light and dark mode only
  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark')
  }

  return (
    <Button
      variant='ghost'
      size='icon'
      className='scale-95 rounded-full transition-all duration-300 hover:bg-primary/10 hover:text-primary dark:hover:bg-primary/20 dark:hover:text-primary'
      onClick={toggleTheme}
    >
      {theme === 'dark' ? (
        <IconMoon className='size-[1.2rem] transition-all duration-300 animate-in fade-in' />
      ) : (
        <IconSun className='size-[1.2rem] transition-all duration-300 animate-in fade-in' />
      )}
      <span className='sr-only'>Toggle theme</span>
    </Button>
  )
}
