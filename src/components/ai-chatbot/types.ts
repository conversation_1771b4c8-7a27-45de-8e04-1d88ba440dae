export interface ToolInvocation {
  state: 'call' | 'result' | 'partial-call' | 'error'
  toolCallId: string
  toolName: string
  args: Record<string, unknown>
  result?: Record<string, unknown>
  error?: string
}

export interface Message {
  id: string
  content: string
  isBot: boolean
  toolInvocations?: ToolInvocation[]
  hasMultipleResults?: boolean
  feedback?: 'like' | 'dislike' | null
  error?: string
}

export interface PromptHistoryItem {
  text: string
  timestamp?: number
}
