import { History, Lightbulb, Plus, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { AIIcon } from './ai-icon'
import { Message, PromptHistoryItem } from './types'

interface ChatHeaderProps {
  messages: Message[]
  userPrompts?: PromptHistoryItem[]
  showSuggestions: boolean
  onToggleSuggestions: () => void
  onShowHistory: () => void
  onNewChat: () => void
  onClose: () => void
}

export function ChatHeader({
  userPrompts = [],
  showSuggestions,
  onToggleSuggestions,
  onShowHistory,
  onNewChat,
  onClose,
}: ChatHeaderProps) {
  return (
    <div className='flex items-center justify-between border-b border-border bg-gradient-to-r from-primary/5 to-background p-4'>
      <div className='flex items-center gap-2'>
        <Avatar className='h-8 w-8 bg-primary/10'>
          <AvatarFallback>
            <AIIcon className='h-4 w-4 text-primary' />
          </AvatarFallback>
        </Avatar>
        <div>
          <h2 className='text-lg font-semibold text-[#00BFE0]'>Luma</h2>
          <p className='text-xs text-muted-foreground'>AI Assistant</p>
        </div>
      </div>
      <div className='flex items-center gap-1'>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant='ghost'
              size='icon'
              onClick={onToggleSuggestions}
              className='relative hover:text-[#00BFE0]'
              aria-label='Toggle suggestions'
            >
              <Lightbulb
                className={cn(
                  'h-5 w-5 transition-all',
                  showSuggestions
                    ? 'animate-pulse text-amber-500'
                    : 'text-[#00BFE0]'
                )}
              />
            </Button>
          </TooltipTrigger>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant='ghost'
              size='icon'
              onClick={onNewChat}
              className='relative text-muted-foreground hover:text-[#00BFE0]'
              aria-label='Start new chat'
            >
              <Plus className='h-5 w-5 text-[#00BFE0]' />
            </Button>
          </TooltipTrigger>
          <TooltipContent side='bottom'>
            <p>New Chat</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant='ghost'
              size='icon'
              onClick={onShowHistory}
              className='relative text-muted-foreground hover:text-foreground'
              aria-label='View history'
            >
              <History
                className={cn(
                  'h-5 w-5 transition-all',
                  userPrompts.length
                    ? 'animate-pulse text-amber-500'
                    : 'text-[#00BFE0]'
                )}
              />
            </Button>
          </TooltipTrigger>
          <TooltipContent side='bottom'>
            <p>View history</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant='ghost'
              size='icon'
              onClick={onClose}
              className='text-[#00BFE0] hover:text-foreground'
              aria-label='Close chat'
            >
              <X className='h-5 w-5' />
            </Button>
          </TooltipTrigger>
          <TooltipContent side='left' align='center'>
            <p>Close chat</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
  )
}
