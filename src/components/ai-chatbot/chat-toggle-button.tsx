import { useState } from 'react'
import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { AIIcon } from './ai-icon'

interface ChatToggleButtonProps {
  isOpen: boolean
  isLoading: boolean
  onClick: () => void
}

export function ChatToggleButton({
  isOpen,
  isLoading,
  onClick,
}: ChatToggleButtonProps) {
  const [isAnimating, setIsAnimating] = useState(false)
  const [iconAnimation, setIconAnimation] = useState<'none' | 'pulse' | 'spin'>(
    'none'
  )

  const handleClick = () => {
    // Only animate if not already animating and not open
    if (!isOpen && !isAnimating) {
      setIsAnimating(true)

      // Set a sequence of animations for the icon
      setIconAnimation('pulse')

      // Change to spin animation after a delay
      setTimeout(() => {
        setIconAnimation('spin')
      }, 750)

      // Add a glow effect to the button
      const button = document.querySelector(
        '.ai-chat-toggle-button'
      ) as HTMLElement
      if (button) {
        button.style.boxShadow = '0 0 20px 5px hsl(var(--primary) / 0.7)'

        // Animate the glow
        setTimeout(() => {
          if (button)
            button.style.boxShadow = '0 0 5px 0px hsl(var(--primary) / 0.5)'
        }, 750)
      }

      // Reset animation states after animation completes
      setTimeout(() => {
        setIsAnimating(false)
        setIconAnimation('none')
        if (button) button.style.removeProperty('box-shadow')
      }, 1500)
    }
    onClick()
  }

  return (
    <div className='fixed bottom-4 right-4 z-50'>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            size='icon'
            className={cn(
              'ai-chat-toggle-button h-14 w-14 rounded-full border-4 border-background bg-gradient-to-r from-primary to-primary/90 shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl',
              isAnimating && 'animate-ai-thinking shadow-xl'
            )}
            onClick={handleClick}
            aria-label='Open Luma AI Assistant'
          >
            {isLoading ? (
              <Loader2 className='h-7 w-7 animate-spin text-primary-foreground' />
            ) : (
              <AIIcon
                className='h-7 w-7 text-primary-foreground'
                animationType={iconAnimation}
              />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent side='top' align='center' className='text-sm'>
          <p>Chat with Luma AI Assistant</p>
        </TooltipContent>
      </Tooltip>
    </div>
  )
}
