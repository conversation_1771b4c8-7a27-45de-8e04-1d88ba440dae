import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { ScrollArea } from '@/components/ui/scroll-area'

// Define types for ReactMarkdown component props
type HeadingProps = React.ComponentPropsWithoutRef<'h1'> & { node?: unknown }
type ParagraphProps = React.ComponentPropsWithoutRef<'p'> & { node?: unknown }
type TableProps = React.ComponentPropsWithoutRef<'table'> & { node?: unknown }
type TableCellProps = React.ComponentPropsWithoutRef<'td'> & { node?: unknown }
type TableRowProps = React.ComponentPropsWithoutRef<'tr'> & { node?: unknown }

interface TextModalProps {
  isOpen: boolean
  onClose: () => void
  content: string
  title?: string
}

export function TextModal({
  isOpen,
  onClose,
  content,
  title = 'Details of your analysis',
}: TextModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-h-[85vh] max-w-4xl overflow-hidden'>
        <DialogHeader>
          <DialogTitle className='mb-2 text-xl font-semibold'>
            {title}
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className='h-[calc(85vh-120px)] pr-4'>
          <div className='prose max-w-none dark:prose-invert prose-headings:mb-3 prose-headings:mt-4 prose-p:my-2 prose-ol:my-2 prose-ul:my-2 prose-li:my-1 prose-table:my-2'>
            <div className='markdown-content markdown-body'>
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                  h1: ({ node, ...props }: HeadingProps) => (
                    <h1 className='mb-3 mt-4 text-xl font-bold' {...props} />
                  ),
                  h2: ({ node, ...props }: HeadingProps) => (
                    <h2
                      className='mb-3 mt-4 text-lg font-semibold'
                      {...props}
                    />
                  ),
                  h3: ({ node, ...props }: HeadingProps) => (
                    <h3
                      className='mb-2 mt-3 text-base font-medium'
                      {...props}
                    />
                  ),
                  p: ({ node, ...props }: ParagraphProps) => (
                    <p className='my-2 text-base' {...props} />
                  ),
                  ul: ({
                    node,
                    ...props
                  }: React.ComponentPropsWithoutRef<'ul'> & {
                    node?: unknown
                  }) => (
                    <ul
                      className='my-2 mb-3 list-disc space-y-1 pl-6'
                      {...props}
                    />
                  ),
                  ol: ({
                    node,
                    ...props
                  }: React.ComponentPropsWithoutRef<'ol'> & {
                    node?: unknown
                  }) => (
                    <ol
                      className='my-2 mb-3 list-decimal space-y-1 pl-6'
                      {...props}
                    />
                  ),
                  li: ({
                    node,
                    ...props
                  }: React.ComponentPropsWithoutRef<'li'> & {
                    node?: unknown
                  }) => <li className='my-1' {...props} />,
                  table: ({ node, ...props }: TableProps) => (
                    <div className='my-3 w-full overflow-x-auto'>
                      <table
                        className='w-full border-collapse text-sm'
                        {...props}
                      />
                    </div>
                  ),
                  thead: ({
                    node,
                    ...props
                  }: React.ComponentPropsWithoutRef<'thead'> & {
                    node?: unknown
                  }) => <thead className='bg-muted/50' {...props} />,
                  tbody: ({
                    node,
                    ...props
                  }: React.ComponentPropsWithoutRef<'tbody'> & {
                    node?: unknown
                  }) => <tbody className='divide-y divide-border' {...props} />,
                  tr: ({ node, ...props }: TableRowProps) => (
                    <tr className='border-b border-border' {...props} />
                  ),
                  th: ({
                    node,
                    ...props
                  }: React.ComponentPropsWithoutRef<'th'> & {
                    node?: unknown
                  }) => (
                    <th
                      className='px-3 py-2 text-left font-medium'
                      {...props}
                    />
                  ),
                  td: ({ node, ...props }: TableCellProps) => (
                    <td className='px-3 py-2' {...props} />
                  ),
                }}
              >
                {content}
              </ReactMarkdown>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
