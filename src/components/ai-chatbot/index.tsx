import { useEffect, useState } from 'react'
import { cn, generateThreadId } from '@/lib/utils'
import { ThreadMessage } from '@/hooks/use-user-messages'
import { TooltipProvider } from '@/components/ui/tooltip'
import { <PERSON>t<PERSON>eader } from './chat-header'
import { ChatInput } from './chat-input'
import { ChatMessages } from './chat-messages'
import { ChatToggleButton } from './chat-toggle-button'
import { HistoryPanel } from './history-panel'
import { useAIChat } from './hooks/use-ai-chat'
import { useChatUIState } from './hooks/use-chat-ui-state'
import { Message } from './types'

const AIChatbot = ({
  firstName,
  resourceId,
}: {
  firstName: string
  resourceId: string
}) => {
  const [threadId, setThreadId] = useState(() => generateThreadId())

  const {
    messages,
    input,
    status,
    error,
    handleInputChange,
    handleSubmit,
    handleSendMessage,
    handleRetry,
    populateMessagesFromThread,
    populateMessagesFromUIMessages,
    resetChat,
    append,
  } = useAIChat({ firstName, threadId, resourceId })

  const [messageFeedback, setMessageFeedback] = useState<
    Record<string, 'like' | 'dislike'>
  >({})

  const messagesWithFeedback: Message[] = messages.map((message) => ({
    ...message,
    feedback: messageFeedback[message.id] || null,
  }))

  const {
    isOpen,
    setIsOpen,
    showHistory,
    setShowHistory,
    showSuggestions,
    setShowSuggestions,
    messagesEndRef,
    scrollToBottom,
    handleKeyDown,
    handleSelectPrompt,
    handleSuggestionClick,
  } = useChatUIState({})

  const isLoading = status === 'submitted' || status === 'streaming'

  const handleFeedback = (messageId: string, feedback: 'like' | 'dislike') => {
    if (feedback === 'dislike') {
      append({
        role: 'user',
        content: `Sorry this info is not correct, can you try again`,
      })
    }
    setMessageFeedback((prev) => {
      const newFeedback = { ...prev }
      if (prev[messageId] === feedback) {
        delete newFeedback[messageId]
      } else {
        newFeedback[messageId] = feedback
      }
      return newFeedback
    })
  }

  // Handle copy message
  const handleCopyMessage = (messageId: string) => {
    const message = messages.find((m) => m.id === messageId)
    if (message) {
      navigator.clipboard.writeText(message.content)
      // You could add a toast notification here
    }
  }

  // Handler for creating a new chat with a new thread ID
  const handleNewChat = () => {
    const newThreadId = generateThreadId()
    setThreadId(newThreadId)
    resetChat()
    setMessageFeedback({}) // Clear feedback state
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages, isLoading, scrollToBottom])

  useEffect(() => {
    if (isOpen) {
      const timeoutId = setTimeout(scrollToBottom, 100)
      return () => clearTimeout(timeoutId)
    }
  }, [isOpen, scrollToBottom])

  return (
    <TooltipProvider>
      <ChatToggleButton
        isOpen={isOpen}
        isLoading={isLoading}
        onClick={() => setIsOpen(true)}
      />

      <div
        className={cn(
          'fixed right-0 top-0 z-50 h-full w-[380px] max-w-full transform border-l border-border bg-background shadow-lg transition-transform duration-300 ease-in-out',
          isOpen ? 'translate-x-0' : 'translate-x-full'
        )}
      >
        <ChatHeader
          messages={messages}
          showSuggestions={showSuggestions}
          onToggleSuggestions={() => setShowSuggestions(!showSuggestions)}
          onShowHistory={() => setShowHistory(true)}
          onNewChat={handleNewChat}
          onClose={() => setIsOpen(false)}
        />
        <div className='flex h-[calc(100%-64px)] flex-col'>
          {status === 'error' && (
            <div className='flex h-full flex-col items-center justify-center gap-4'>
              <p className='text-center text-muted-foreground'>
                {error?.message?.includes('Provider returned error') ||
                error?.message?.includes('ERROR') ||
                error?.message?.includes('400')
                  ? 'There was an issue with the conversation. This has been automatically fixed.'
                  : 'Something went wrong, please try again.'}
              </p>
              <div className='flex gap-3'>
                <button
                  type='button'
                  onClick={handleRetry}
                  disabled={isLoading}
                  className='rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50'
                >
                  {isLoading ? 'Retrying...' : 'Try Again'}
                </button>
              </div>
            </div>
          )}
          <ChatMessages
            messages={messagesWithFeedback}
            isLoading={isLoading}
            messagesEndRef={messagesEndRef}
            onFeedback={handleFeedback}
            onCopyMessage={handleCopyMessage}
            onRetry={handleRetry}
          />
          <ChatInput
            inputValue={input}
            isLoading={isLoading}
            showSuggestions={showSuggestions}
            onInputChange={handleInputChange}
            onKeyDown={(e) => handleKeyDown(e, handleSubmit)}
            onSendMessage={() => handleSubmit()}
            onSelectPrompt={(prompt) =>
              handleSuggestionClick(prompt, handleSendMessage)
            }
          />
        </div>
      </div>

      <HistoryPanel
        showHistory={showHistory}
        onClose={() => setShowHistory(false)}
        onSelectPrompt={(prompt) =>
          handleSelectPrompt(prompt, handleInputChange)
        }
        onAppendMessages={(messages: ThreadMessage[]) =>
          populateMessagesFromThread(messages)
        }
        onLoadThreadMessages={populateMessagesFromUIMessages}
        onSetThreadId={setThreadId}
        resourceId={resourceId}
      />
    </TooltipProvider>
  )
}

export default AIChatbot
