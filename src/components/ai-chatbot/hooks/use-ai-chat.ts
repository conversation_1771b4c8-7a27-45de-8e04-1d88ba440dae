import { useQueryClient } from '@tanstack/react-query'
import { useChat } from '@ai-sdk/react'
import { API_URL } from '@/api/ai-api'
import { UIMessage } from '@/hooks/use-thread-messages'
import { ThreadMessage } from '@/hooks/use-user-messages'
import { token } from '../token'
import { Message, ToolInvocation } from '../types'

const initialMessage =
  "I'm **Luma** 👋\n\nI'm your AI assistant. I can help you with:\n\n- **Data analysis** and visualization\n- **Business insights** and recommendations\n- **Taking actions** based on your requests\n\nHow can I assist you today?"

export function useAIChat({
  firstName,
  threadId,
  resourceId,
}: {
  firstName: string
  threadId?: string
  resourceId?: string
}) {
  const queryClient = useQueryClient()

  const {
    messages: chatMessages,
    input,
    handleInputChange,
    handleSubmit,
    status,
    append,
    error,
    setMessages,
  } = useChat({
    api: `${API_URL}agents/getEntityRecordAgent/stream`,
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
      Accept: 'application/json',
      'Transfer-Encoding': 'chunked',
      Connection: 'keep-alive',
    },

    experimental_prepareRequestBody: (request) => {
      // Get the last message from the request
      const lastMessage =
        request.messages.length > 0
          ? request.messages[request.messages.length - 1]
          : null

      // Return only the last message along with resource ID, thread ID, and streaming configuration
      return {
        messages: [lastMessage],
        resourceId: String(resourceId),
        threadId,
        token,
      }
    },
    onFinish: async () => {
      await queryClient.invalidateQueries({
        queryKey: ['user-messages', resourceId],
      })
    },
    onError: (err) => {
      // Don't append error messages to the stream anymore
      // Errors will be handled in the UI components
      // eslint-disable-next-line no-console
      console.error('AI Chat Error:', err)
    },
    maxSteps: 10,
    initialMessages: [
      {
        id: '1',
        content: `Hi **${firstName}**, ${initialMessage}`,
        role: 'assistant',
      },
    ],
  })

  const messages: Message[] = chatMessages.map((msg) => {
    const toolInvocations =
      (msg.parts
        ?.filter((part) => part.type === 'tool-invocation')
        ?.map((part) =>
          part.type === 'tool-invocation' ? part.toolInvocation : null
        )
        ?.filter(Boolean) as ToolInvocation[]) || []

    const hasMultipleResults = toolInvocations.some((tool) => {
      if (tool.state === 'result' && tool.result) {
        if (Array.isArray(tool.result) && tool.result.length > 5) {
          return true
        }

        if (
          tool.result.data &&
          Array.isArray(tool.result.data) &&
          tool.result.data.length > 5
        ) {
          return true
        }
      }
      return false
    })

    // Check if this message has an error (from the global error state)
    const messageError =
      error &&
      msg.role === 'assistant' &&
      msg.id === chatMessages[chatMessages.length - 1]?.id
        ? error.message
        : undefined

    return {
      id: msg.id,
      content: msg.content || '',
      isBot: msg.role === 'assistant',
      toolInvocations: toolInvocations.length > 0 ? toolInvocations : undefined,
      hasMultipleResults,
      error: messageError,
    }
  })

  const handleSendMessage = (manualPrompt: string): boolean => {
    const textToSend = manualPrompt || input
    if (textToSend.trim() === '') return false

    append({
      role: 'user',
      content: textToSend,
    })

    return true
  }

  const handleRetry = () => {
    // Find the last user message to retry
    const lastUserMessage = [...messages].reverse().find((msg) => !msg.isBot)

    if (lastUserMessage) {
      // Show a retry message to the user
      append({
        role: 'assistant',
        content: 'Let me try again...',
      })

      // Short delay before retrying to ensure UI updates
      setTimeout(() => {
        // Retry with the last user message content
        append({
          role: 'user',
          content: lastUserMessage.content,
        })
      }, 500)
    } else {
      // If no user message found, just reload to start fresh
      window.location.reload()
    }
  }

  // TODO: Refactor this
  // Function to append thread messages to the chat without making API calls
  const populateMessagesFromThread = (threadMessages: ThreadMessage[]) => {
    // Sort messages so user messages come first, followed by assistant messages
    const sortedMessages = [...threadMessages].sort((a, b) => {
      // If roles are different, put user messages first
      if (a.role !== b.role) {
        return a.role === 'user' ? -1 : 1
      }
      // If roles are the same, maintain original order by timestamp
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    })

    // Get the current messages in the chat
    const currentMessages = chatMessages.map((msg) => ({
      id: msg.id,
      role: msg.role,
      content: msg.content || '',
    }))

    // Convert ThreadMessage[] to the format expected by setMessages
    const threadFormattedMessages = sortedMessages.map((msg) => ({
      id: msg.id,
      role: msg.role,
      content: msg.content,
    }))

    // Append the thread messages to the current messages
    const combinedMessages = [...currentMessages, ...threadFormattedMessages]

    // Set the combined messages directly without making API calls
    setMessages(combinedMessages)
  }

  // Function to populate messages from UI messages (from useThreadMessages)
  const populateMessagesFromUIMessages = (uiMessages: UIMessage[]) => {
    // Sort messages by timestamp to maintain conversation order
    const sortedMessages = [...uiMessages].sort((a, b) => {
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    })

    // Convert UIMessage[] to the format expected by setMessages
    const formattedMessages = sortedMessages.map((msg) => ({
      id: msg.id,
      role: msg.role,
      content: msg.content,
    }))

    // Replace current messages with the thread messages
    setMessages(formattedMessages)
  }

  // Function to reset chat with initial message
  const resetChat = () => {
    setMessages([
      {
        id: '1',
        content: `Hi **${firstName}**, ${initialMessage}`,
        role: 'assistant',
      },
    ])
  }

  return {
    messages,
    input,
    status,
    error,
    handleInputChange,
    handleSubmit,
    handleSendMessage,
    handleRetry,
    append,
    populateMessagesFromThread,
    populateMessagesFromUIMessages,
    resetChat,
  }
}
