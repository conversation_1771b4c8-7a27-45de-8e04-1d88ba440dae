import { useState, useRef } from 'react'

interface UseChatUIStateOptions {
  onMessageSent?: () => void
}

export function useChatUIState({ onMessageSent }: UseChatUIStateOptions = {}) {
  const [isOpen, setIsOpen] = useState(false)
  const [showHistory, setShowHistory] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)

  const messagesEndRef = useRef<HTMLDivElement>(
    null
  ) as React.RefObject<HTMLDivElement>

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleKeyDown = (e: React.KeyboardEvent, onSubmit: () => void) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      onSubmit()
    }
  }

  const handleSelectPrompt = (
    prompt: string,
    handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  ) => {
    handleInputChange({
      target: { value: prompt },
    } as React.ChangeEvent<HTMLInputElement>)
    setShowHistory(false)
  }

  const handleSuggestionClick = (
    prompt: string,
    handleSendMessage: (prompt: string) => boolean
  ) => {
    const sent = handleSendMessage(prompt)
    if (sent) {
      setShowSuggestions(false)
      if (!isOpen) {
        setIsOpen(true)
      }
      if (onMessageSent) {
        onMessageSent()
      }
    }
  }

  return {
    isOpen,
    setIsOpen,
    showHistory,
    setShowHistory,
    showSuggestions,
    setShowSuggestions,
    messagesEndRef,
    scrollToBottom,
    handleKeyDown,
    handleSelectPrompt,
    handleSuggestionClick,
  }
}
