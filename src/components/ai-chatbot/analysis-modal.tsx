// Imports (keep at top in real file)
import type { HTMLAttributes } from 'react'
import {
  <PERSON><PERSON>dingUp,
  BarChart3,
  Sparkles,
  Brain,
  Lightbulb,
  Zap,
  ChevronRight,
} from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Separator } from '@/components/ui/separator'
import { SmartChart } from './charts'
import { ToolInvocation } from './types'

// Domain Types
interface AnalysisModalProps {
  isOpen: boolean
  onClose: () => void
  toolInvocations: ToolInvocation[]
  messageContent: string
  onRetry?: () => void
}

// Utility Functions (Pure)
const getAnalysisTitle = (content: string): string => {
  return content.match(/\*\*Title:\*\*\s*\n\*\*(.+?)\*\*/)?.[1] || ''
}

// Markdown Component Configurations
const createMarkdownComponents = () => ({
  h1: (props: HTMLAttributes<HTMLHeadingElement>) => (
    <h1 className='mb-3 mt-4 text-xl font-bold' {...props} />
  ),
  h2: (props: HTMLAttributes<HTMLHeadingElement>) => (
    <h2 className='mb-3 mt-4 text-lg font-semibold' {...props} />
  ),
  h3: (props: HTMLAttributes<HTMLHeadingElement>) => (
    <h3 className='mb-2 mt-3 text-base font-medium' {...props} />
  ),
  p: (props: HTMLAttributes<HTMLParagraphElement>) => (
    <p className='my-2 text-base' {...props} />
  ),
  ul: (props: HTMLAttributes<HTMLUListElement>) => (
    <ul className='my-2 mb-3 list-disc space-y-1 pl-6' {...props} />
  ),
  ol: (props: HTMLAttributes<HTMLOListElement>) => (
    <ol className='my-2 mb-3 list-decimal space-y-1 pl-6' {...props} />
  ),
  li: (props: HTMLAttributes<HTMLLIElement>) => (
    <li className='my-1' {...props} />
  ),
  table: (props: HTMLAttributes<HTMLTableElement>) => (
    <div className='my-3 w-full overflow-x-auto'>
      <table className='w-full border-collapse text-sm' {...props} />
    </div>
  ),
  thead: (props: HTMLAttributes<HTMLTableSectionElement>) => (
    <thead className='bg-muted/50' {...props} />
  ),
  tbody: (props: HTMLAttributes<HTMLTableSectionElement>) => (
    <tbody className='divide-y divide-border' {...props} />
  ),
  tr: (props: HTMLAttributes<HTMLTableRowElement>) => (
    <tr className='border-b border-border' {...props} />
  ),
  th: (props: HTMLAttributes<HTMLTableCellElement>) => (
    <th className='px-3 py-2 text-left font-medium' {...props} />
  ),
  td: (props: HTMLAttributes<HTMLTableCellElement>) => (
    <td className='px-3 py-2' {...props} />
  ),
})

// Sub-components
const ModalHeader = ({ title }: { title: string }) => (
  <DialogHeader className='relative overflow-hidden'>
    {/* Background decoration */}
    <div className='absolute inset-0 bg-gradient-to-r from-primary/5 via-purple-500/5 to-blue-500/5' />
    <div className='absolute -right-4 -top-4 h-24 w-24 rounded-full bg-gradient-to-br from-primary/10 to-purple-500/10 blur-xl' />
    <div className='absolute -bottom-2 -left-2 h-16 w-16 rounded-full bg-gradient-to-tr from-blue-500/10 to-primary/10 blur-lg' />

    <DialogTitle className='relative'>
      <div className='flex items-start gap-4 p-6'>
        {/* Animated icon container */}
        <div className='relative flex-shrink-0'>
          <div className='absolute inset-0 animate-pulse rounded-full bg-gradient-to-r from-primary to-purple-600 opacity-20 blur-sm' />
          <div className='relative rounded-full bg-gradient-to-r from-primary to-purple-600 p-3 shadow-lg'>
            <Sparkles className='h-6 w-6 animate-pulse text-white' />
          </div>
          {/* Floating sparkles */}
          <div className='absolute -right-1 -top-1 h-2 w-2 animate-ping rounded-full bg-yellow-400' />
          <div className='absolute -bottom-1 -left-1 h-1.5 w-1.5 animate-ping rounded-full bg-blue-400 delay-300' />
        </div>

        {/* Title content */}
        <div className='flex-1 space-y-2'>
          <div className='flex items-center gap-2'>
            <Badge
              variant='secondary'
              className='border-primary/20 bg-primary/10 text-primary'
            >
              <Brain className='mr-1 h-3 w-3' />
              AI Analysis
            </Badge>
            <Badge
              variant='outline'
              className='border-purple-200 text-purple-700'
            >
              <BarChart3 className='mr-1 h-3 w-3' />
              Data Insights
            </Badge>
          </div>
          <h2 className='bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-2xl font-bold text-transparent dark:from-gray-100 dark:to-gray-400'>
            {title || 'Data Analysis & Insights'}
          </h2>
          <p className='flex items-center gap-1 text-sm text-muted-foreground'>
            <Zap className='h-3 w-3 text-yellow-500' />
            Powered by Luma • Real-time insights
          </p>
        </div>
      </div>
    </DialogTitle>
  </DialogHeader>
)

const ChartSection = ({
  toolInvocations,
  onRetry,
}: {
  toolInvocations: ToolInvocation[]
  onRetry?: () => void
}) => (
  <div className='relative overflow-hidden rounded-xl border border-border/50 bg-gradient-to-br from-background via-background to-muted/20 p-6 shadow-lg'>
    {/* Background decoration */}
    <div className='absolute inset-0 bg-gradient-to-br from-transparent via-primary/[0.02] to-purple-500/[0.02]' />
    <div className='absolute -right-8 -top-8 h-32 w-32 rounded-full bg-gradient-to-br from-primary/5 to-transparent blur-2xl' />
    <div className='absolute -bottom-8 -left-8 h-24 w-24 rounded-full bg-gradient-to-tr from-blue-500/5 to-transparent blur-xl' />

    {/* Header */}
    <div className='relative mb-6 flex items-center justify-between'>
      <div className='flex items-center gap-3'>
        <div className='rounded-lg bg-gradient-to-r from-primary/10 to-purple-500/10 p-2'>
          <TrendingUp className='h-5 w-5 text-primary' />
        </div>
        <div>
          <h3 className='text-lg font-semibold text-foreground'>
            Visual Analytics
          </h3>
          <p className='text-sm text-muted-foreground'>
            Interactive data visualization
          </p>
        </div>
      </div>
    </div>

    {/* Chart container */}
    <div className='relative h-[400px] w-full overflow-hidden rounded-lg border border-border/30 bg-background/50 backdrop-blur-sm'>
      <div className='absolute inset-0 bg-gradient-to-t from-background/10 to-transparent' />
      <SmartChart
        toolInvocations={toolInvocations}
        height={400}
        onRetry={onRetry}
      />
    </div>
  </div>
)

const AnalysisSection = ({ content }: { content: string }) => (
  <div className='relative overflow-hidden rounded-xl border border-border/50 bg-gradient-to-br from-background via-muted/10 to-background p-8 shadow-lg'>
    {/* Background decoration */}
    <div className='absolute inset-0 bg-gradient-to-br from-transparent via-purple-500/[0.02] to-blue-500/[0.02]' />
    <div className='absolute -right-12 -top-12 h-40 w-40 rounded-full bg-gradient-to-br from-purple-500/5 to-transparent blur-3xl' />
    <div className='absolute -bottom-6 -left-6 h-28 w-28 rounded-full bg-gradient-to-tr from-primary/5 to-transparent blur-2xl' />

    {/* Header with enhanced styling */}
    <div className='relative mb-8 flex items-start justify-between'>
      <div className='flex items-start gap-4'>
        {/* Animated icon container */}
        <div className='relative flex-shrink-0'>
          <div className='absolute inset-0 animate-pulse rounded-xl bg-gradient-to-r from-purple-500 to-blue-500 opacity-20 blur-sm' />
          <div className='relative rounded-xl bg-gradient-to-r from-purple-500 to-blue-500 p-3 shadow-lg'>
            <Lightbulb className='h-6 w-6 text-white' />
          </div>
          {/* Floating elements */}
          <div className='absolute -right-1 -top-1 h-2 w-2 animate-bounce rounded-full bg-yellow-400' />
          <div className='absolute -bottom-1 -left-1 h-1.5 w-1.5 animate-bounce rounded-full bg-green-400 delay-150' />
        </div>

        <div className='space-y-2'>
          <div className='flex items-center gap-2'>
            <h3 className='bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-xl font-bold text-transparent'>
              Analysis & Insights
            </h3>
            <ChevronRight className='h-4 w-4 animate-pulse text-muted-foreground' />
          </div>
          <p className='flex items-center gap-1 text-sm text-muted-foreground'>
            <Brain className='h-3 w-3 text-purple-500' />
            AI-powered analysis • Key findings & recommendations
          </p>
        </div>
      </div>

      <Badge
        variant='secondary'
        className='border-purple-200 bg-purple-50 text-purple-700'
      >
        <Sparkles className='mr-1 h-3 w-3' />
        Insights
      </Badge>
    </div>

    {/* Content with enhanced styling */}
    <div className='relative'>
      <div className='prose prose-lg max-w-none dark:prose-invert prose-headings:mb-4 prose-headings:mt-6 prose-headings:font-bold prose-headings:text-foreground prose-p:my-3 prose-p:leading-relaxed prose-blockquote:border-l-primary prose-blockquote:bg-muted/30 prose-blockquote:py-2 prose-strong:text-foreground prose-code:rounded prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-ol:my-4 prose-ul:my-4 prose-li:my-2 prose-table:my-6'>
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={createMarkdownComponents()}
        >
          {content}
        </ReactMarkdown>
      </div>
    </div>
  </div>
)

// Main Component (Composition)
export function AnalysisModal({
  isOpen,
  onClose,
  toolInvocations,
  messageContent,
  onRetry,
}: AnalysisModalProps) {
  const modalTitle = getAnalysisTitle(messageContent) || 'Analysis Results'

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-h-[90vh] max-w-6xl overflow-y-auto border-0 bg-gradient-to-br from-background via-background to-muted/10 p-0 shadow-2xl'>
        {/* Background decoration for the entire modal */}
        <div className='absolute inset-0 bg-gradient-to-br from-transparent via-primary/[0.01] to-purple-500/[0.01]' />
        <div className='from-primary/3 absolute -right-20 -top-20 h-60 w-60 rounded-full bg-gradient-to-br to-transparent blur-3xl' />
        <div className='from-blue-500/3 absolute -bottom-20 -left-20 h-48 w-48 rounded-full bg-gradient-to-tr to-transparent blur-3xl' />

        {/* Content */}
        <div className='relative'>
          <ModalHeader title={modalTitle} />

          <div className='space-y-8 p-6 pt-0'>
            <ChartSection toolInvocations={toolInvocations} onRetry={onRetry} />

            {/* Enhanced separator */}
            <div className='relative flex items-center justify-center py-4'>
              <Separator className='flex-1 bg-gradient-to-r from-transparent via-border to-transparent' />
              <div className='mx-4 rounded-full bg-gradient-to-r from-primary/10 to-purple-500/10 p-2'>
                <ChevronRight className='h-4 w-4 text-muted-foreground' />
              </div>
              <Separator className='flex-1 bg-gradient-to-r from-border via-border to-transparent' />
            </div>

            <AnalysisSection content={messageContent} />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
