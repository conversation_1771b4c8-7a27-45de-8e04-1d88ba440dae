import { useState, useEffect } from 'react'
import { format, isToday, isYesterday, formatDistanceToNow } from 'date-fns'
import { ArrowLeft, History, Clock } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useThreadMessages } from '@/hooks/use-thread-messages'
import type { UIMessage } from '@/hooks/use-thread-messages'
import { useUserMessages } from '@/hooks/use-user-messages'
import type { ThreadMessage } from '@/hooks/use-user-messages'
import { Button } from '@/components/ui/button'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'

interface HistoryPanelProps {
  showHistory: boolean
  onClose: () => void
  onSelectPrompt: (text: string) => void
  onAppendMessages: (messages: ThreadMessage[]) => void
  onLoadThreadMessages: (uiMessages: UIMessage[]) => void
  onSetThreadId: (threadId: string) => void
  resourceId?: string
}

const formatTimestamp = (timestamp: number | string) => {
  const date = new Date(timestamp)

  if (isToday(date)) {
    return `Today at ${format(date, 'h:mm a')}`
  }

  if (isYesterday(date)) {
    return `Yesterday at ${format(date, 'h:mm a')}`
  }

  const daysDifference = Math.floor(
    (Date.now() - date.getTime()) / (1000 * 60 * 60 * 24)
  )
  if (daysDifference < 7) {
    return `${formatDistanceToNow(date, { addSuffix: true })}`
  }

  return format(date, "MMM d, yyyy 'at' h:mm a")
}

export function HistoryPanel({
  showHistory,
  onClose,
  onSelectPrompt: _onSelectPrompt,
  onAppendMessages: _onAppendMessages,
  onLoadThreadMessages,
  onSetThreadId,
  resourceId,
}: HistoryPanelProps) {
  const { data } = useUserMessages(resourceId)
  const [selectedThreadId, setSelectedThreadId] = useState<string | null>(null)

  //TODO: Use Url to get threadId
  // Use the thread messages hook for the selected thread
  const { data: threadMessagesData, isLoading: isLoadingMessages } =
    useThreadMessages(selectedThreadId || '')

  // Effect to handle when thread messages are loaded
  useEffect(() => {
    if (threadMessagesData?.uiMessages && selectedThreadId) {
      // Set the current thread ID to the selected thread so new messages continue in this thread
      onSetThreadId(selectedThreadId)
      // Use UI messages for better display
      onLoadThreadMessages(threadMessagesData.uiMessages)
      onClose()
      setSelectedThreadId(null) // Reset selection
    }
  }, [
    threadMessagesData,
    selectedThreadId,
    onLoadThreadMessages,
    onSetThreadId,
    onClose,
  ])

  const handleThreadClick = (threadId: string) => setSelectedThreadId(threadId)

  return (
    <div
      className={cn(
        'fixed right-0 top-0 z-50 h-full w-96 max-w-full transform border-l border-border bg-background shadow-lg transition-transform duration-300 ease-in-out',
        showHistory ? 'translate-x-0' : 'translate-x-full'
      )}
    >
      <div className='flex items-center justify-between border-b border-border bg-gradient-to-r from-primary/5 to-background p-4'>
        <div className='flex items-center gap-2'>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant='ghost'
                size='icon'
                onClick={onClose}
                className='text-muted-foreground hover:text-foreground'
                aria-label='Back to chat'
              >
                <ArrowLeft className='h-5 w-5' />
              </Button>
            </TooltipTrigger>
            <TooltipContent side='right'>
              <p>Back to chat</p>
            </TooltipContent>
          </Tooltip>
          <div>
            <h2 className='text-lg font-semibold'>Chat History</h2>
            <p className='text-xs text-muted-foreground'>
              Your previous conversations
            </p>
          </div>
        </div>
      </div>
      <div className='flex h-[calc(100%-64px)] flex-col'>
        <div className='flex-1 overflow-y-auto bg-gradient-to-b from-background to-background/80 p-4'>
          {!data?.length ? (
            <div className='flex h-full flex-col items-center justify-center gap-4'>
              <div className='rounded-full bg-primary/10 p-6'>
                <History className='h-10 w-10 text-primary/60' />
              </div>
              <p className='text-center text-muted-foreground'>
                No history yet
              </p>
              <p className='max-w-xs text-center text-xs text-muted-foreground'>
                Your conversation history will appear here so you can easily
                revisit previous questions.
              </p>
            </div>
          ) : (
            <div className='space-y-3'>
              {data?.map((thread) => {
                return (
                  <div
                    key={thread.id}
                    className={cn(
                      'cursor-pointer rounded-lg border border-border bg-card/50 p-3 shadow-sm transition-all hover:border-primary/20 hover:bg-primary/5 hover:shadow-md',
                      isLoadingMessages &&
                        selectedThreadId === thread.id &&
                        'cursor-not-allowed opacity-50'
                    )}
                    onClick={() => {
                      // Use the new thread messages loading functionality
                      if (!isLoadingMessages) {
                        handleThreadClick(thread.id)
                      }
                    }}
                  >
                    <div className='flex flex-col gap-2'>
                      <p className='line-clamp-2 text-sm font-medium'>
                        {thread.title?.startsWith('New Thread')
                          ? `Chat Session ${thread.id.slice(0, 8)}...`
                          : thread.title ||
                            `Thread ${thread.id.slice(0, 8)}...`}
                      </p>
                      <div className='flex items-center gap-1.5 text-xs text-muted-foreground'>
                        <Clock className='h-3 w-3 text-muted-foreground/70' />
                        <span>{formatTimestamp(thread.createdAt)}</span>
                      </div>
                      {thread.metadata?.workingMemory && (
                        <p className='line-clamp-1 text-xs text-muted-foreground/70'>
                          {thread.metadata.workingMemory.trim().slice(0, 100)}
                          ...
                        </p>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
