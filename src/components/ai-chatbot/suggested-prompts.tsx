import { AIIcon } from './ai-icon'

interface SuggestedPromptsProps {
  prompts?: string[]
  onSelectPrompt: (prompt: string) => void
}

const suggestedPrompts = [
  'Show me my top 10 performing classes for last month',
  'What is the the member ratio for this month compared to last month',
  'Compare the total reservations for last 3 months',
  'Can you compare the top 10 performing classes of last year with this year',
  'Can you show me the top 5 performing trainers of last year',
]

export function SuggestedPrompts({
  prompts = suggestedPrompts,
  onSelectPrompt,
}: SuggestedPromptsProps) {
  return (
    <div className='mb-4 space-y-2 rounded-lg border border-amber-200/20 bg-amber-50/10 p-3 shadow-sm duration-300 animate-in fade-in-0 slide-in-from-bottom-3 dark:bg-amber-900/5'>
      <div className='space-y-2'>
        <div className='flex items-center gap-1.5'>
          <AIIcon className='h-3.5 w-3.5 text-amber-500' />
          <p className='text-xs font-medium text-muted-foreground'>
            Try asking about your business data:
          </p>
        </div>
      </div>
      <div className='flex flex-wrap gap-2'>
        {prompts.map((prompt, index) => (
          <button
            key={index}
            type='button'
            className='rounded-full border border-amber-200/30 bg-amber-50/20 px-3 py-1 text-left text-xs text-[#1B617A] transition-colors hover:bg-amber-100/30 dark:text-amber-400 dark:hover:bg-amber-900/20'
            onClick={() => onSelectPrompt(prompt)}
          >
            {prompt}
          </button>
        ))}
      </div>
    </div>
  )
}
