import { <PERSON><PERSON><PERSON>, ChevronUp } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Message } from '../types'

interface AnalysisToggleButtonsProps {
  message: Message
  isExpanded: boolean
  isLoading: boolean
  onToggle: () => void
  onViewDetailed: () => void
}

export function AnalysisToggleButtons({
  isExpanded,
  isLoading,
  onToggle,
  onViewDetailed,
}: AnalysisToggleButtonsProps) {
  if (isExpanded) {
    return (
      <Button
        variant='outline'
        size='sm'
        className={cn(
          'mb-3 flex w-full items-center justify-center gap-1 text-xs',
          isLoading && 'pointer-events-none opacity-50'
        )}
        onClick={onToggle}
        disabled={isLoading}
        aria-disabled={isLoading}
      >
        <ChevronUp className='h-3 w-3' />
        Hide Detailed Analysis
      </Button>
    )
  }

  return (
    <Button
      variant='default'
      size='sm'
      className={cn(
        'flex w-full items-center justify-center gap-1 text-xs',
        isLoading && 'pointer-events-none opacity-50'
      )}
      onClick={onViewDetailed}
      disabled={isLoading}
      aria-disabled={isLoading}
    >
      <BarChart className='mr-1 h-3 w-3' />
      View Detailed Analysis
    </Button>
  )
}
