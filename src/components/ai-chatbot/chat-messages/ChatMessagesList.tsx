import React, { useEffect, useState } from 'react'
import { AnalysisModal } from '../analysis-modal'
import { ThinkingIndicator } from '../thinking-indicator'
import { Message } from '../types'
import { MessageItem } from './MessageItem'

interface ChatMessagesListProps {
  messages: Message[]
  isLoading: boolean
  messagesEndRef: React.RefObject<HTMLDivElement>
  onFeedback: (messageId: string, feedback: 'like' | 'dislike') => void
  onCopyMessage: (messageId: string) => void
  onRetry?: () => void
}

//TODO: Refactor this ai generated code
export function ChatMessagesList({
  messages,
  isLoading,
  messagesEndRef,
  onFeedback,
  onCopyMessage,
  onRetry,
}: ChatMessagesListProps) {
  const [showAnimatedWelcome, setShowAnimatedWelcome] = useState(false)
  // Track which messages with multiple results are expanded
  const [expandedAnalysis, setExpandedAnalysis] = useState<
    Record<string, boolean>
  >({})

  // Keep track of messages that have multiple results
  const [messagesWithMultipleResultsIds, setMessagesWithMultipleResultsIds] =
    useState<string[]>([])

  // Modal state
  const [modalOpen, setModalOpen] = useState(false)
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null)

  // Update our tracking of messages with multiple results
  useEffect(() => {
    // Find messages that should be tracked
    const messagesToTrack = messages
      .filter((msg) => {
        // If it has multiple results flag set
        if (msg.hasMultipleResults && msg.toolInvocations) {
          return true
        }

        // If it's in our existing tracking list
        if (
          messagesWithMultipleResultsIds.includes(msg.id) &&
          msg.toolInvocations
        ) {
          return true
        }

        // Check if this message has tool invocations with multiple data points
        if (msg.toolInvocations && msg.toolInvocations.length > 0) {
          const hasMultipleDataPoints = msg.toolInvocations.some((tool) => {
            if (tool.state === 'result' && tool.result) {
              // If result is an array with more than 5 items
              if (Array.isArray(tool.result) && tool.result.length > 5) {
                return true
              }
              // If result has a data property that is an array with more than 5 items
              if (
                tool.result.data &&
                Array.isArray(tool.result.data) &&
                tool.result.data.length > 1
              ) {
                return true
              }
            }
            return false
          })

          return hasMultipleDataPoints
        }

        return false
      })
      .map((msg) => msg.id)

    // Update our tracking list if it's changed
    if (
      JSON.stringify(messagesToTrack) !==
      JSON.stringify(messagesWithMultipleResultsIds)
    ) {
      setMessagesWithMultipleResultsIds(messagesToTrack)
    }
  }, [messages, messagesWithMultipleResultsIds])

  // Toggle analysis view for a specific message
  const toggleAnalysisView = (messageId: string) => {
    // Only toggle if this is a valid message with multiple results
    if (messagesWithMultipleResultsIds.includes(messageId)) {
      setExpandedAnalysis((prev) => {
        // Get current state (default to false if not set)
        const currentState = prev[messageId] || false

        return {
          ...prev,
          [messageId]: !currentState,
        }
      })
    }
  }

  // Open the analysis modal with the selected message
  const openAnalysisModal = (message: Message) => {
    // Set the selected message and open the modal
    setSelectedMessage(message)
    setModalOpen(true)

    // Make sure this message stays in collapsed state
    if (message.id && message.toolInvocations) {
      // Add this message to our tracking if it's not already there
      if (!messagesWithMultipleResultsIds.includes(message.id)) {
        setMessagesWithMultipleResultsIds((prev) => [...prev, message.id])
      }

      // Force update the expanded state to ensure this message stays collapsed
      setExpandedAnalysis((prev) => {
        // Only update if needed
        if (prev[message.id] !== false) {
          return {
            ...prev,
            [message.id]: false,
          }
        }
        return prev
      })
    }
  }

  // Show animated welcome when the component mounts and there's only one message (the initial one)
  useEffect(() => {
    // Use a ref to track if we've already shown the animation for this set of messages
    const shouldShowAnimation = messages.length === 1 && messages[0].isBot

    if (shouldShowAnimation && !showAnimatedWelcome) {
      // Small delay to ensure the animation is noticeable
      const timer = setTimeout(() => {
        setShowAnimatedWelcome(true)
      }, 100)

      return () => clearTimeout(timer)
    } else if (!shouldShowAnimation && showAnimatedWelcome) {
      setShowAnimatedWelcome(false)
    }
  }, [messages, showAnimatedWelcome])

  // Initialize and maintain expanded state for messages with multiple results
  useEffect(() => {
    // Create a new state object based on the current messages
    const newExpandedState: Record<string, boolean> = {}

    // First, copy existing state for messages that still exist
    messagesWithMultipleResultsIds.forEach((messageId) => {
      // If we already have a state for this message, keep it
      if (Object.prototype.hasOwnProperty.call(expandedAnalysis, messageId)) {
        newExpandedState[messageId] = expandedAnalysis[messageId]
      } else {
        // Otherwise initialize as collapsed (false)
        newExpandedState[messageId] = false
      }
    })

    // Only update if the state has actually changed
    if (JSON.stringify(newExpandedState) !== JSON.stringify(expandedAnalysis)) {
      setExpandedAnalysis(newExpandedState)
    }
  }, [messagesWithMultipleResultsIds, expandedAnalysis])

  return (
    <>
      {selectedMessage && (
        <AnalysisModal
          isOpen={modalOpen}
          onClose={() => setModalOpen(false)}
          toolInvocations={selectedMessage.toolInvocations || []}
          messageContent={selectedMessage.content}
          onRetry={onRetry}
        />
      )}
      <div
        className='flex-1 space-y-4 overflow-y-auto overflow-x-hidden bg-gradient-to-b from-background to-background/80 p-4'
        id='chat-messages'
      >
        {messages.map((message) => (
          <MessageItem
            key={message.id}
            message={message}
            isLoading={isLoading}
            isMessageWithMultipleResults={messagesWithMultipleResultsIds.includes(
              message.id
            )}
            isAnalysisExpanded={expandedAnalysis[message.id] || false}
            onToggleAnalysis={toggleAnalysisView}
            onOpenAnalysisModal={openAnalysisModal}
            onFeedback={onFeedback}
            onCopyMessage={onCopyMessage}
            onRetry={onRetry}
          />
        ))}
        {isLoading && <ThinkingIndicator />}
        <div ref={messagesEndRef} />
      </div>
    </>
  )
}
