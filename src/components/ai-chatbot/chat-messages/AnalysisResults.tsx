import { ErrorDisplay } from '../charts/ErrorDisplay'
import { ToolInvocation } from '../types'

interface AnalysisResultsProps {
  toolInvocations: ToolInvocation[]
  onRetry?: () => void
}

export function AnalysisResults({
  toolInvocations,
  onRetry,
}: AnalysisResultsProps) {
  if (!toolInvocations || toolInvocations.length === 0) return null

  // Check for errors first
  const errorTool = toolInvocations.find((tool) => tool.state === 'error')
  if (errorTool && errorTool.error && onRetry) {
    return (
      <div className='mt-3'>
        <ErrorDisplay error={errorTool.error} onRetry={onRetry} height={200} />
      </div>
    )
  }

  // Filter to only show results
  const results = toolInvocations.filter(
    (tool) => tool.state === 'result' && tool.result
  )

  if (results.length === 0) return null

  return (
    <div className='mt-3 space-y-3'>
      {results.map((tool) => {
        // Determine what data to display
        let displayData: Record<string, unknown> = tool.result || {}

        // If result has a data property that's an array, use that
        if (
          tool.result &&
          tool.result.data &&
          Array.isArray(tool.result.data)
        ) {
          // Convert array to a Record with indices as keys
          displayData = { data: tool.result.data }
        }

        return (
          <div
            key={tool.toolCallId}
            className='rounded-md border border-border bg-muted/30 p-3'
          >
            <h4 className='mb-1 font-medium'>{tool.toolName}</h4>
            <div className='text-sm text-muted-foreground'>
              <pre className='overflow-x-auto whitespace-pre-wrap'>
                {typeof displayData === 'object'
                  ? JSON.stringify(displayData, null, 2)
                  : String(displayData)}
              </pre>
            </div>
          </div>
        )
      })}
    </div>
  )
}
