import { ThumbsUp, ThumbsDown, Copy } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'

interface MessageFeedbackProps {
  messageId: string
  feedback?: 'like' | 'dislike' | null | undefined
  onFeedback: (messageId: string, feedback: 'like' | 'dislike') => void
  onCopy: (messageId: string) => void
}

export function MessageFeedback({
  messageId,
  feedback,
  onFeedback,
  onCopy,
}: MessageFeedbackProps) {
  const handleLike = () => {
    onFeedback(messageId, 'like')
  }

  const handleDislike = () => {
    onFeedback(messageId, 'dislike')
  }

  const handleCopy = () => {
    onCopy(messageId)
  }

  return (
    <div className='mt-2 flex items-center gap-1'>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant='ghost'
            size='sm'
            onClick={handleLike}
            className={cn(
              'h-7 w-7 p-0 text-muted-foreground hover:text-green-600',
              feedback === 'like' &&
                'bg-green-50 text-green-600 hover:bg-green-100'
            )}
          >
            <ThumbsUp className='h-3.5 w-3.5' />
          </Button>
        </TooltipTrigger>
        <TooltipContent
          side='bottom'
          className='border-black bg-black text-white'
        >
          <p>Good response</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant='ghost'
            size='sm'
            onClick={handleDislike}
            className={cn(
              'h-7 w-7 p-0 text-muted-foreground hover:text-red-600',
              feedback === 'dislike' &&
                'bg-red-50 text-red-600 hover:bg-red-100'
            )}
          >
            <ThumbsDown className='h-3.5 w-3.5' />
          </Button>
        </TooltipTrigger>
        <TooltipContent
          side='bottom'
          className='border-black bg-black text-white'
        >
          <p>Poor response</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant='ghost'
            size='sm'
            onClick={handleCopy}
            className='h-7 w-7 p-0 text-muted-foreground hover:text-blue-600'
          >
            <Copy className='h-3.5 w-3.5' />
          </Button>
        </TooltipTrigger>
        <TooltipContent
          side='bottom'
          className='border-black bg-black text-white'
        >
          <p>Copy message</p>
        </TooltipContent>
      </Tooltip>
    </div>
  )
}
