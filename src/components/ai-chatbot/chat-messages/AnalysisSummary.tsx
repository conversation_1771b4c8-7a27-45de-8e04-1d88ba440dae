import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { Message } from '../types'

// Define types for ReactMarkdown component props
type HeadingProps = React.ComponentPropsWithoutRef<'h1'> & { node?: unknown }
type ParagraphProps = React.ComponentPropsWithoutRef<'p'> & { node?: unknown }

interface AnalysisSummaryProps {
  message: Message
}

export function AnalysisSummary({ message }: AnalysisSummaryProps) {
  return (
    <div className='mb-2 rounded-md border border-border bg-muted/20 p-3'>
      <h4 className='mb-2 font-medium'>Analysis Results</h4>
      {/* Display the first part of the message content as a summary */}
      <div className='prose dark:prose-invert prose-p:my-1 prose-headings:mt-2 prose-headings:mb-1 mb-3'>
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={{
            // Override default styling for certain elements
            p: ({ node, ...props }: ParagraphProps) => (
              <p className='my-1 text-sm' {...props} />
            ),
            h1: ({ node, ...props }: HeadingProps) => (
              <h1 className='text-lg font-bold' {...props} />
            ),
            h2: ({ node, ...props }: HeadingProps) => (
              <h2 className='text-base font-semibold' {...props} />
            ),
            h3: ({ node, ...props }: HeadingProps) => (
              <h3 className='text-sm font-medium' {...props} />
            ),
          }}
        >
          {/* Extract the first paragraph or two from the message content */}
          {message.content.split('\n\n').slice(0, 2).join('\n\n')}
        </ReactMarkdown>
      </div>
    </div>
  )
}
