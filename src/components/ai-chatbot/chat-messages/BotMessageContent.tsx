import { useState } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
// Note: Will be replaced with Streamdown.ai in the embeddable widget
import { Button } from '@/components/ui/button'
import { TextModal } from '../text-modal'
import { Message } from '../types'

// Define types for ReactMarkdown component props
type HeadingProps = React.ComponentPropsWithoutRef<'h1'> & { node?: unknown }
type ParagraphProps = React.ComponentPropsWithoutRef<'p'> & { node?: unknown }
type AnchorProps = React.ComponentPropsWithoutRef<'a'> & { node?: unknown }
type CodeProps = React.ComponentPropsWithoutRef<'code'> & {
  node?: unknown
  inline?: boolean
}
type TableProps = React.ComponentPropsWithoutRef<'table'> & { node?: unknown }
type TableCellProps = React.ComponentPropsWithoutRef<'td'> & { node?: unknown }
type TableRowProps = React.ComponentPropsWithoutRef<'tr'> & { node?: unknown }

interface BotMessageContentProps {
  message: Message
  shouldShowContent: boolean
}

export function BotMessageContent({
  message,
  shouldShowContent,
}: BotMessageContentProps) {
  const [isModalOpen, setIsModalOpen] = useState(false)

  // Character limit for truncating text (approximately 1000 characters)
  const TEXT_LIMIT = 500
  const isTextTooLong = message.content.length > TEXT_LIMIT

  if (!shouldShowContent) {
    return null
  }

  // Function to get truncated content with proper word boundaries
  const getTruncatedContent = (): string => {
    if (!isTextTooLong) return message.content

    // Truncate at a good breakpoint (end of sentence or paragraph)
    const truncated = message.content.substring(0, TEXT_LIMIT)

    // Try to find the last paragraph break
    const lastParagraphBreak = truncated.lastIndexOf('\n\n')
    if (lastParagraphBreak > TEXT_LIMIT * 0.7) {
      return message.content.substring(0, lastParagraphBreak) + '\n\n...'
    }

    // Try to find the last sentence break
    const lastSentenceBreak = truncated.match(/[.!?]\s/g)
    if (lastSentenceBreak && lastSentenceBreak.length > 0) {
      const lastIndex =
        truncated.lastIndexOf(lastSentenceBreak[lastSentenceBreak.length - 1]) +
        1
      if (lastIndex > TEXT_LIMIT * 0.7) {
        return message.content.substring(0, lastIndex) + '...'
      }
    }

    // Fallback to word boundary
    const lastSpaceIndex = truncated.lastIndexOf(' ')
    return message.content.substring(0, lastSpaceIndex) + '...'
  }

  return (
    <div className='w-full overflow-x-auto'>
      {isModalOpen && (
        <TextModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          content={message.content}
          title='Full Message'
        />
      )}
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        children={isTextTooLong ? getTruncatedContent() : message.content}
        components={{
          // Override default styling for certain elements
          h1: ({ node, ...props }: HeadingProps) => (
            <h1 className='mb-1 mt-2 text-lg font-semibold' {...props} />
          ),
          h2: ({ node, ...props }: HeadingProps) => (
            <h2 className='mb-1 mt-2 text-base font-medium' {...props} />
          ),
          h3: ({ node, ...props }: HeadingProps) => (
            <h3 className='mb-1 mt-2 text-sm font-medium' {...props} />
          ),
          p: ({ node, ...props }: ParagraphProps) => (
            <p className='my-1 text-base' {...props} />
          ),
          a: ({ node, ...props }: AnchorProps) => (
            <a className='text-primary underline' {...props} />
          ),
          // Table styling
          table: ({ node, ...props }: TableProps) => (
            <div className='my-2 w-full overflow-x-auto'>
              <table
                className='w-full min-w-full table-fixed border-collapse text-xs md:text-sm'
                {...props}
              />
            </div>
          ),
          thead: ({ node, ...props }) => (
            <thead className='bg-muted/50' {...props} />
          ),
          tbody: ({ node, ...props }) => (
            <tbody className='divide-y divide-border' {...props} />
          ),
          tr: ({ node, ...props }: TableRowProps) => (
            <tr className='border-b border-border' {...props} />
          ),
          th: ({ node, ...props }) => (
            <th
              className='truncate px-2 py-2 text-left text-xs font-medium text-muted-foreground md:px-3'
              {...props}
            />
          ),
          td: ({ node, ...props }: TableCellProps) => (
            <td
              className='truncate px-2 py-2 align-middle text-xs md:px-3 md:text-sm'
              {...props}
            />
          ),
          // Code styling
          code: ({ node, inline, ...props }: CodeProps) =>
            inline ? (
              <code
                className='rounded bg-muted/50 px-1 py-0.5 text-sm'
                {...props}
              />
            ) : (
              <code
                className='my-2 block overflow-x-auto rounded bg-muted/20 p-2 text-sm'
                {...props}
              />
            ),
        }}
      />

      {isTextTooLong && (
        <div className='mt-2'>
          <Button
            variant='outline'
            size='sm'
            onClick={() => setIsModalOpen(true)}
            className='text-xs'
          >
            Read full message
          </Button>
        </div>
      )}
    </div>
  )
}
