import { cn } from '@/lib/utils'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { AIIcon } from '../ai-icon'
import { Message } from '../types'
import { UserIcon } from '../user-icon'
import { AnalysisResults } from './AnalysisResults'
import { AnalysisSummary } from './AnalysisSummary'
import { AnalysisToggleButtons } from './AnalysisToggleButtons'
import { BotMessageContent } from './BotMessageContent'
import { MessageFeedback } from './MessageFeedback'
import { UserMessageContent } from './UserMessageContent'

interface MessageItemProps {
  message: Message
  isLoading: boolean
  isMessageWithMultipleResults: boolean
  isAnalysisExpanded: boolean
  onToggleAnalysis: (messageId: string) => void
  onOpenAnalysisModal: (message: Message) => void
  onFeedback: (messageId: string, feedback: 'like' | 'dislike') => void
  onCopyMessage: (messageId: string) => void
  onRetry?: () => void
}

export function MessageItem({
  message,
  isLoading,
  isMessageWithMultipleResults,
  isAnalysisExpanded,
  onToggleAnalysis,
  onOpenAnalysisModal,
  onFeedback,
  onCopyMessage,
  onRetry,
}: MessageItemProps) {
  const handleToggleAnalysis = () => {
    onToggleAnalysis(message.id)
  }

  const handleOpenAnalysisModal = () => {
    onOpenAnalysisModal(message)
  }

  // Determine if we should show the message content
  const shouldShowContent =
    !message.isBot || !isMessageWithMultipleResults || isAnalysisExpanded

  // Check if this is the initial greeting message (should not show feedback buttons)
  const isInitialMessage = message.id === '1' && message.isBot

  return (
    <div
      className={cn(
        'flex items-start gap-2',
        message.isBot ? 'self-start' : 'flex-row-reverse self-end'
      )}
    >
      <Avatar
        className={cn(
          'h-8 w-8',
          message.isBot ? 'bg-primary/10' : 'border border-black bg-white'
        )}
      >
        <AvatarFallback>
          {message.isBot ? (
            <AIIcon className='h-4 w-4 text-primary' />
          ) : (
            <UserIcon className='h-3 w-3 text-black' />
          )}
        </AvatarFallback>
      </Avatar>
      <div
        className={cn(
          'overflow-hidden rounded-lg p-3 shadow-sm duration-300 animate-in fade-in-0 slide-in-from-bottom-3',
          message.isBot
            ? 'w-[85%] max-w-[85%] rounded-tl-none bg-muted text-black'
            : 'max-w-[75%] rounded-tr-none border border-black bg-white text-black'
        )}
      >
        <div
          className={cn(
            'prose w-full max-w-none',
            message.isBot
              ? 'dark:prose-invert prose-headings:mb-1 prose-headings:mt-2 prose-h1:text-lg prose-h2:text-base prose-h3:text-sm prose-p:my-1 prose-ol:my-1 prose-ul:my-1 prose-li:my-0.5 prose-table:w-full prose-table:text-sm'
              : ''
          )}
        >
          {message.isBot ? (
            <>
              <BotMessageContent
                message={message}
                shouldShowContent={shouldShowContent}
              />

              {/* Show analysis summary and view button if there are multiple results */}
              {isMessageWithMultipleResults && message.toolInvocations && (
                <div className='mt-3'>
                  {!isAnalysisExpanded ? (
                    <>
                      <AnalysisSummary message={message} />
                      <AnalysisToggleButtons
                        message={message}
                        isExpanded={false}
                        isLoading={isLoading}
                        onToggle={handleToggleAnalysis}
                        onViewDetailed={handleOpenAnalysisModal}
                      />
                    </>
                  ) : (
                    <>
                      <AnalysisToggleButtons
                        message={message}
                        isExpanded={true}
                        isLoading={isLoading}
                        onToggle={handleToggleAnalysis}
                        onViewDetailed={handleOpenAnalysisModal}
                      />
                      <AnalysisResults
                        toolInvocations={message.toolInvocations}
                        onRetry={onRetry}
                      />
                    </>
                  )}
                </div>
              )}

              {/* Add feedback buttons for bot messages (except initial greeting) */}
              {!isInitialMessage && (
                <MessageFeedback
                  messageId={message.id}
                  feedback={message.feedback}
                  onFeedback={onFeedback}
                  onCopy={onCopyMessage}
                />
              )}
            </>
          ) : (
            <UserMessageContent content={message.content} />
          )}
        </div>
      </div>
    </div>
  )
}
