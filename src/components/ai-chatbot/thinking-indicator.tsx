import { useEffect, useState } from 'react'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { AIIcon } from './ai-icon'

export function ThinkingIndicator() {
  const [dots, setDots] = useState('.')

  // Create an animated dots effect
  useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => {
        if (prev === '.') return '..'
        if (prev === '..') return '...'
        return '.'
      })
    }, 500)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className='flex items-start gap-2 self-start'>
      <Avatar className='h-8 w-8 bg-primary/10'>
        <AvatarFallback>
          <AIIcon className='h-4 w-4 text-primary' pulseEffect />
        </AvatarFallback>
      </Avatar>
      <div className='max-w-[75%] rounded-lg rounded-tl-none bg-muted p-3 shadow-sm duration-300 animate-in fade-in-0 slide-in-from-bottom-3'>
        <div className='flex items-center gap-1.5'>
          <p className='flex items-center text-xs italic text-muted-foreground'>
            Luma is thinking{dots}
          </p>
        </div>
      </div>
    </div>
  )
}
