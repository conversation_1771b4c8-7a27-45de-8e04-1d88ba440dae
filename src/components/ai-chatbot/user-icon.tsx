interface UserIconProps {
  className?: string
}

export function UserIcon({ className = 'h-6 w-6' }: UserIconProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='1.75'
      strokeLinecap='round'
      strokeLinejoin='round'
      className={className}
    >
      {/* Modern user icon with abstract face and details */}
      <circle cx='12' cy='12' r='10' strokeOpacity='0.2' />
      <path
        d='M12 2a10 10 0 0 1 10 10c0 5.5-4.5 10-10 10S2 17.5 2 12A10 10 0 0 1 12 2'
        strokeOpacity='0.1'
      />

      {/* Head */}
      <circle cx='12' cy='8' r='4' />

      {/* Body */}
      <path d='M20 19v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2' />

      {/* Details */}
      <path d='M9.5 9a.5.5 0 1 0 0-1 .5.5 0 0 0 0 1z' fill='currentColor' />
      <path d='M14.5 9a.5.5 0 1 0 0-1 .5.5 0 0 0 0 1z' fill='currentColor' />
      <path d='M9 11.5c.5 1 1.5 1.5 3 1.5s2.5-.5 3-1.5' strokeOpacity='0.8' />

      {/* Additional details for more personality */}
      <path d='M12 16v1' strokeOpacity='0.6' />
      <path d='M8 17h8' strokeOpacity='0.4' />
    </svg>
  )
}
