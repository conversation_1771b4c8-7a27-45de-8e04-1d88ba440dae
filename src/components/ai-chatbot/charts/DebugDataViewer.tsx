import { ToolInvocation } from '../types'

interface DebugDataViewerProps {
  toolInvocations: ToolInvocation[]
}

export function DebugDataViewer({ toolInvocations }: DebugDataViewerProps) {
  if (!toolInvocations || toolInvocations.length === 0) {
    return (
      <div className='rounded-md border border-yellow-200 bg-yellow-50 p-4'>
        <h4 className='font-medium text-yellow-800'>Debug: No Tool Invocations</h4>
        <p className='text-sm text-yellow-700'>No tool invocations found</p>
      </div>
    )
  }

  return (
    <div className='rounded-md border border-blue-200 bg-blue-50 p-4'>
      <h4 className='mb-2 font-medium text-blue-800'>Debug: Tool Invocations Data</h4>
      <div className='space-y-3'>
        {toolInvocations.map((tool, index) => (
          <div key={tool.toolCallId || index} className='rounded border border-blue-300 bg-white p-3'>
            <div className='mb-2'>
              <span className='font-medium text-blue-900'>Tool:</span> {tool.toolName}
            </div>
            <div className='mb-2'>
              <span className='font-medium text-blue-900'>State:</span> {tool.state}
            </div>
            {tool.result && (
              <div>
                <span className='font-medium text-blue-900'>Result Structure:</span>
                <pre className='mt-1 max-h-40 overflow-auto rounded bg-gray-100 p-2 text-xs'>
                  {JSON.stringify(tool.result, null, 2)}
                </pre>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
