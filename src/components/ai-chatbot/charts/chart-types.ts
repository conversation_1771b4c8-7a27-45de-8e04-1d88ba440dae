export type ChartType = 'bar' | 'line' | 'area' | 'pie' | 'scatter' | 'comparison'

export interface ChartDataPoint {
  [key: string]: string | number | null | undefined
}

export interface ProcessedChartData {
  data: ChartDataPoint[]
  keys: string[]
  xAxisKey: string
  chartType: ChartType
  title?: string
  description?: string
}

export interface ChartConfig {
  type: ChartType
  data: ChartDataPoint[]
  xAxisKey: string
  dataKeys: string[]
  colors: string[]
  title?: string
  description?: string
}

export interface DataAnalysis {
  hasNumericData: boolean
  hasTimeData: boolean
  hasCategoricalData: boolean
  dataPointCount: number
  numericKeys: string[]
  categoricalKeys: string[]
  timeKeys: string[]
  isComparison: boolean
  suggestedChartType: ChartType
}

// Color palette for charts
export const CHART_COLORS = [
  '#00BFE0', // Primary brand color
  '#8B5CF6', // Purple
  '#10B981', // Green
  '#F59E0B', // Orange
  '#EF4444', // Red
  '#6366F1', // Indigo
  '#EC4899', // Pink
  '#14B8A6', // Teal
  '#F97316', // Orange-red
  '#84CC16', // Lime
] as const

export const CHART_CSS_VARIABLES = [
  'var(--chart-1)',
  'var(--chart-2)', 
  'var(--chart-3)',
  'var(--chart-4)',
  'var(--chart-5)',
] as const
