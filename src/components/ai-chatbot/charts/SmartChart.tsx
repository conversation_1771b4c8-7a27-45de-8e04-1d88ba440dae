import {
  Bar,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  LineChart,
  Area,
  Area<PERSON>hart,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Cell,
} from 'recharts'
import type { TooltipProps } from 'recharts'
import { ToolInvocation } from '../types'
import { ErrorDisplay } from './ErrorDisplay'
import { processChartData, getChartColor } from './chart-utils'

interface SmartChartProps {
  toolInvocations: ToolInvocation[]
  height?: number
  onRetry?: () => void
}

export function SmartChart({
  toolInvocations,
  height = 400,
  onRetry,
}: SmartChartProps) {
  // Check for errors in tool invocations first
  const errorTool = toolInvocations.find((tool) => tool.state === 'error')
  if (errorTool && errorTool.error && onRetry) {
    return (
      <ErrorDisplay error={errorTool.error} onRetry={onRetry} height={height} />
    )
  }

  const chartData = processChartD<PERSON>(toolInvocations)

  if (!chartData || chartData.data.length === 0) {
    return (
      <div className='flex h-[400px] items-center justify-center'>
        <p className='text-muted-foreground'>No chart data available</p>
      </div>
    )
  }

  const { data, keys, xAxisKey, chartType } = chartData

  // Validate data structure
  if (!data || data.length === 0) {
    return (
      <div className='flex h-[400px] items-center justify-center'>
        <p className='text-muted-foreground'>No valid data points found</p>
      </div>
    )
  }

  // Simple validation: check if we have name and count fields
  const hasValidData = data.every((item) => {
    const hasName = item.name !== undefined && item.name !== null
    const hasCount =
      item.count !== undefined &&
      item.count !== null &&
      typeof item.count === 'number' &&
      item.count >= 0
    return hasName && hasCount
  })

  if (!hasValidData) {
    return (
      <div className='flex h-[400px] items-center justify-center'>
        <div className='text-center'>
          <p className='text-muted-foreground'>Invalid chart data structure</p>
          <p className='mt-1 text-xs text-muted-foreground'>
            Each item must have 'name' and 'count' fields
          </p>
          <details className='mt-2 text-xs'>
            <summary>Debug Info</summary>
            <pre className='mt-1 text-left'>
              {JSON.stringify(data, null, 2)}
            </pre>
          </details>
        </div>
      </div>
    )
  }

  // Custom tooltip component
  const CustomTooltip = ({
    active,
    payload,
    label,
  }: TooltipProps<number, string>) => {
    if (active && payload && payload.length) {
      return (
        <div className='rounded-md border border-border bg-background p-3 shadow-lg'>
          <p className='mb-2 font-medium'>{label}</p>
          {payload.map((entry, index) => (
            <p key={index} className='text-sm' data-color={entry.color}>
              <span
                className='mr-2 inline-block h-3 w-3 rounded-full bg-current'
                style={{
                  backgroundColor: entry.color || '#8884d8',
                  color: entry.color || '#8884d8',
                }}
              />
              {`${entry.name}: ${entry.value}`}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  // Render different chart types
  const renderChart = () => {
    const commonProps = {
      data,
      margin: { top: 20, right: 30, left: 20, bottom: 5 },
    }

    switch (chartType) {
      case 'line':
        return (
          <LineChart {...commonProps}>
            <CartesianGrid
              strokeDasharray='3 3'
              vertical={false}
              opacity={0.3}
            />
            <XAxis
              dataKey={xAxisKey}
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
            />
            <YAxis tick={{ fontSize: 12 }} tickLine={false} axisLine={false} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            {keys.map((key, index) => (
              <Line
                key={key}
                type='monotone'
                dataKey={key}
                stroke={getChartColor(index)}
                strokeWidth={2}
                dot={{ fill: getChartColor(index), strokeWidth: 2, r: 4 }}
                activeDot={{
                  r: 6,
                  stroke: getChartColor(index),
                  strokeWidth: 2,
                }}
              />
            ))}
          </LineChart>
        )

      case 'area':
        return (
          <AreaChart {...commonProps}>
            <CartesianGrid
              strokeDasharray='3 3'
              vertical={false}
              opacity={0.3}
            />
            <XAxis
              dataKey={xAxisKey}
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
            />
            <YAxis tick={{ fontSize: 12 }} tickLine={false} axisLine={false} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            {keys.map((key, index) => (
              <Area
                key={key}
                type='monotone'
                dataKey={key}
                stackId='1'
                stroke={getChartColor(index)}
                fill={getChartColor(index)}
                fillOpacity={0.6}
              />
            ))}
          </AreaChart>
        )

      case 'pie': {
        const pieData = data.map((item, index) => ({
          name: item[xAxisKey],
          value: item[keys[0]],
          fill: getChartColor(index),
        }))

        return (
          <PieChart>
            <Pie
              data={pieData}
              cx='50%'
              cy='50%'
              labelLine={false}
              label={({ name, percent }) =>
                `${name} ${(percent * 100).toFixed(0)}%`
              }
              outerRadius={120}
              fill='#8884d8'
              dataKey='value'
            >
              {pieData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
          </PieChart>
        )
      }

      case 'scatter':
        return (
          <ScatterChart {...commonProps}>
            <CartesianGrid strokeDasharray='3 3' opacity={0.3} />
            <XAxis
              type='number'
              dataKey={keys[0]}
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
            />
            <YAxis
              type='number'
              dataKey={keys[1]}
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
            />
            <Tooltip
              content={<CustomTooltip />}
              cursor={{ strokeDasharray: '3 3' }}
            />
            <Scatter name='Data Points' data={data} fill={getChartColor(0)} />
          </ScatterChart>
        )

      case 'comparison':
        return (
          <BarChart {...commonProps}>
            <CartesianGrid
              strokeDasharray='3 3'
              vertical={false}
              opacity={0.3}
            />
            <XAxis
              dataKey={xAxisKey}
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
            />
            <YAxis tick={{ fontSize: 12 }} tickLine={false} axisLine={false} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            {keys.map((key, index) => (
              <Bar
                key={key}
                dataKey={key}
                fill={getChartColor(index)}
                radius={[2, 2, 0, 0]}
                name={key}
              />
            ))}
          </BarChart>
        )

      default: // 'bar'
        return (
          <BarChart {...commonProps}>
            <CartesianGrid
              strokeDasharray='3 3'
              vertical={false}
              opacity={0.3}
            />
            <XAxis
              dataKey={xAxisKey}
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
            />
            <YAxis tick={{ fontSize: 12 }} tickLine={false} axisLine={false} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar
              dataKey='count'
              fill={getChartColor(0)}
              radius={[4, 4, 0, 0]}
              name='Count'
            />
          </BarChart>
        )
    }
  }

  return (
    <div className='w-full' style={{ height: `${height}px` }}>
      <ResponsiveContainer width='100%' height='100%'>
        {renderChart()}
      </ResponsiveContainer>
    </div>
  )
}
