import { AlertCircle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface ErrorDisplayProps {
  error: string
  onRetry: () => void
  height?: number
  isRetrying?: boolean
}

export function ErrorDisplay({ 
  error, 
  onRetry, 
  height = 400, 
  isRetrying = false 
}: ErrorDisplayProps) {
  const getErrorMessage = (error: string) => {
    if (error.includes('Failed to fetch') || error.includes('NetworkError')) {
      return 'Unable to connect to the AI service. Please check your internet connection and try again.'
    }
    
    if (error.includes('timeout') || error.includes('Timeout')) {
      return 'The AI service is taking too long to respond. Please try again later.'
    }
    
    if (error.includes('401') || error.includes('Unauthorized')) {
      return 'Authentication failed. Please refresh the page and try again.'
    }
    
    if (error.includes('500') || error.includes('Internal Server Error')) {
      return 'The AI service is experiencing issues. Please try again in a few moments.'
    }
    
    return error || 'An unexpected error occurred while generating the analysis.'
  }

  return (
    <div 
      className='flex items-center justify-center p-6' 
      style={{ height: `${height}px` }}
    >
      <div className='w-full max-w-md space-y-4'>
        <Alert variant="destructive" className='border-red-200 bg-red-50'>
          <AlertCircle className='h-4 w-4' />
          <AlertDescription className='text-sm'>
            {getErrorMessage(error)}
          </AlertDescription>
        </Alert>
        
        <div className='flex justify-center'>
          <Button 
            onClick={onRetry}
            disabled={isRetrying}
            variant="outline"
            size="sm"
            className='min-w-[120px]'
          >
            {isRetrying ? (
              <>
                <RefreshCw className='mr-2 h-4 w-4 animate-spin' />
                Retrying...
              </>
            ) : (
              <>
                <RefreshCw className='mr-2 h-4 w-4' />
                Try Again
              </>
            )}
          </Button>
        </div>
        
        <div className='text-center'>
          <p className='text-xs text-muted-foreground'>
            If the problem persists, please contact support
          </p>
        </div>
      </div>
    </div>
  )
}
