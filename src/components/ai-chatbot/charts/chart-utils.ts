import { ToolInvocation } from '../types'
import {
  ChartDataPoint,
  ProcessedChartData,
  DataAnalysis,
  ChartType,
  CHART_COLORS,
} from './chart-types'

/**
 * Extract chart data from tool invocations - SIMPLIFIED VERSION
 */
export function extractChartData(
  toolInvocations: ToolInvocation[]
): ChartDataPoint[] {
  if (!toolInvocations || toolInvocations.length === 0) {
    return []
  }

  // Find all tool invocations with results
  const toolsWithData = toolInvocations.filter(
    (tool) => tool.state === 'result' && tool.result
  )

  if (toolsWithData.length === 0) {
    return []
  }

  // Process each tool result
  const allData: ChartDataPoint[] = []

  for (const tool of toolsWithData) {
    const extractedData = tool.result ? extractDataFromResult(tool.result) : []

    if (extractedData.length > 0) {
      allData.push(...extractedData)
    }
  }

  return allData
}

/**
 * Extract data from a single tool result - SIMPLIFIED VERSION
 */
function extractDataFromResult(
  result: Record<string, unknown>
): ChartDataPoint[] {
  // Case 1: result.data is an array
  if (result.data && Array.isArray(result.data)) {
    return simpleProcessArray(result.data)
  }

  // Case 2: result itself is an array
  if (Array.isArray(result)) {
    return simpleProcessArray(result)
  }

  // Case 3: Look for any arrays in the result
  const arrays = Object.entries(result).filter(([_, value]) =>
    Array.isArray(value)
  )

  for (const [_, array] of arrays) {
    const processed = simpleProcessArray(array as unknown[])
    if (processed.length > 0) {
      return processed
    }
  }

  return []
}

/**
 * Simple array processing - just convert to chart format
 */
function simpleProcessArray(array: unknown[]): ChartDataPoint[] {
  if (!array || array.length === 0) return []

  const processed = array
    .filter(
      (item): item is Record<string, unknown> =>
        typeof item === 'object' && item !== null
    )
    .map((item, index) => {
      // Create a simple chart data point
      const chartPoint: ChartDataPoint = {}

      // Find name field - ensure it's a string
      const nameValue =
        item.name || item.title || item.label || item.id || `Item ${index + 1}`
      chartPoint.name =
        typeof nameValue === 'string' || typeof nameValue === 'number'
          ? String(nameValue)
          : `Item ${index + 1}`

      // Find count field - ensure it's a number
      const countValue =
        Number(item.reservation_count) ||
        Number(item.count) ||
        Number(item.total) ||
        Number(item.value) ||
        0
      chartPoint.count = isNaN(countValue) ? 0 : countValue

      // Keep other fields - ensure they match the allowed types
      Object.keys(item).forEach((key) => {
        if (
          ![
            'name',
            'title',
            'label',
            'reservation_count',
            'count',
            'total',
            'value',
          ].includes(key)
        ) {
          const value = item[key]
          // Only assign values that match the ChartDataPoint type
          if (
            typeof value === 'string' ||
            typeof value === 'number' ||
            value === null ||
            value === undefined
          ) {
            chartPoint[key] = value
          }
        }
      })

      return chartPoint
    })
    .filter((item) => {
      // Ensure count exists and is a number greater than 0
      return typeof item.count === 'number' && item.count > 0
    })

  return processed
}

/**
 * Analyze data characteristics to determine the best chart type
 */
export function analyzeData(data: ChartDataPoint[]): DataAnalysis {
  if (data.length === 0) {
    return {
      hasNumericData: false,
      hasTimeData: false,
      hasCategoricalData: false,
      dataPointCount: 0,
      numericKeys: [],
      categoricalKeys: [],
      timeKeys: [],
      isComparison: false,
      suggestedChartType: 'bar',
    }
  }

  const firstItem = data[0]
  const keys = Object.keys(firstItem)

  const numericKeys: string[] = []
  const categoricalKeys: string[] = []
  const timeKeys: string[] = []

  // Analyze each key to determine its type
  keys.forEach((key) => {
    const values = data.map((item) => item[key]).filter((val) => val != null)
    if (values.length === 0) return

    const sampleValue = values[0]

    // Check if it's a time/date field
    if (isTimeKey(key, sampleValue)) {
      timeKeys.push(key)
    }
    // Check if it's numeric (including string numbers)
    else if (
      values.every((val) => {
        if (typeof val === 'number') return true
        if (typeof val === 'string') {
          const num = Number(val)
          return !isNaN(num) && isFinite(num)
        }
        return false
      })
    ) {
      numericKeys.push(key)
    }
    // Otherwise it's categorical
    else {
      categoricalKeys.push(key)
    }
  })

  const hasNumericData = numericKeys.length > 0
  const hasTimeData = timeKeys.length > 0
  const hasCategoricalData = categoricalKeys.length > 0

  // Better comparison detection - check for actual comparative data
  const isComparison = detectComparison(data, numericKeys)

  // Determine suggested chart type
  const suggestedChartType = determineBestChartType({
    hasNumericData,
    hasTimeData,
    hasCategoricalData,
    dataPointCount: data.length,
    numericKeys,
    categoricalKeys,
    timeKeys,
    isComparison,
    suggestedChartType: 'bar', // placeholder
  })

  return {
    hasNumericData,
    hasTimeData,
    hasCategoricalData,
    dataPointCount: data.length,
    numericKeys,
    categoricalKeys,
    timeKeys,
    isComparison,
    suggestedChartType,
  }
}

/**
 * Check if a key represents time/date data
 */
function isTimeKey(key: string, sampleValue: unknown): boolean {
  const timeKeywords = [
    'date',
    'time',
    'created',
    'updated',
    'month',
    'year',
    'day',
  ]
  const keyLower = key.toLowerCase()

  // Check if key name suggests time
  if (timeKeywords.some((keyword) => keyLower.includes(keyword))) {
    return true
  }

  // Check if value looks like a date
  if (typeof sampleValue === 'string') {
    const dateRegex =
      /^\d{4}-\d{2}-\d{2}|^\d{2}\/\d{2}\/\d{4}|^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)/i
    return dateRegex.test(sampleValue)
  }

  return false
}

/**
 * Detect if data represents a comparison (e.g., same items across different years)
 */
function detectComparison(
  data: ChartDataPoint[],
  numericKeys: string[]
): boolean {
  if (data.length < 2) return false

  // Check if we have year data and duplicate names
  const hasYearData = data.some((item) => item.year !== undefined)
  if (hasYearData) {
    const names = data.map((item) => item.name)
    const uniqueNames = new Set(names)
    // If we have fewer unique names than total items, we likely have comparisons
    if (uniqueNames.size < names.length) {
      return true
    }
  }

  // Check if we have multiple numeric columns that could represent different periods
  if (numericKeys.length > 1) {
    // Look for patterns like "2023_count", "2024_count" or similar
    const yearPattern = /\d{4}/
    const hasYearColumns = numericKeys.some((key) => yearPattern.test(key))
    if (hasYearColumns) {
      return true
    }
  }

  return false
}

/**
 * Determine the best chart type based on data analysis
 */
function determineBestChartType(analysis: DataAnalysis): ChartType {
  const {
    hasTimeData,
    hasNumericData,
    dataPointCount,
    numericKeys,
    isComparison,
  } = analysis

  // If we have time data, prefer line or area charts
  if (hasTimeData && hasNumericData) {
    return numericKeys.length > 1 ? 'area' : 'line'
  }

  // For comparison of multiple numeric values
  if (isComparison && numericKeys.length > 1) {
    return 'comparison'
  }

  // For small datasets with categories, consider pie chart
  if (dataPointCount <= 6 && hasNumericData && numericKeys.length === 1) {
    return 'pie'
  }

  // For scatter plots (two numeric variables)
  if (numericKeys.length === 2 && dataPointCount > 5) {
    return 'scatter'
  }

  // Default to bar chart
  return 'bar'
}

/**
 * Process raw data into chart-ready format
 */
export function processChartData(
  toolInvocations: ToolInvocation[]
): ProcessedChartData | null {
  const rawData = extractChartData(toolInvocations)

  if (rawData.length === 0) {
    return null
  }

  // Simple approach: always use 'name' for X-axis and 'count' for Y-axis
  const processedData = {
    data: rawData,
    keys: ['count'],
    xAxisKey: 'name',
    chartType: 'bar' as ChartType,
    title: extractChartTitle(toolInvocations),
    description: `Analysis of ${rawData.length} data points`,
  }

  return processedData
}

/**
 * Extract a title from tool invocations
 */
function extractChartTitle(toolInvocations: ToolInvocation[]): string {
  if (!toolInvocations || toolInvocations.length === 0) {
    return 'Data Visualization'
  }

  const toolWithData = toolInvocations.find(
    (tool) => tool.state === 'result' && tool.result
  )

  return toolWithData
    ? `${toolWithData.toolName} Results`
    : 'Data Visualization'
}

/**
 * Get color for chart elements
 */
export function getChartColor(index: number): string {
  return CHART_COLORS[index % CHART_COLORS.length]
}
