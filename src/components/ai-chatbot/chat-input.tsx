import React, { useRef, useEffect } from 'react'
import { Send } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { SuggestedPrompts } from './suggested-prompts'

interface ChatInputProps {
  inputValue: string
  isLoading: boolean
  showSuggestions: boolean
  onInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  onKeyDown: (e: React.KeyboardEvent) => void
  onSendMessage: () => void
  onSelectPrompt: (prompt: string) => void
}

export function ChatInput({
  inputValue,
  isLoading,
  showSuggestions,
  onInputChange,
  onKeyDown,
  onSendMessage,
  onSelectPrompt,
}: ChatInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Auto-resize textarea based on content
  useEffect(() => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`
    }
  }, [inputValue])

  return (
    <div className='border-t border-border bg-background/95 p-4 backdrop-blur-sm'>
      {/* Suggested prompts - show when showSuggestions is true */}
      {showSuggestions ? (
        <SuggestedPrompts onSelectPrompt={onSelectPrompt} />
      ) : null}
      <div className='flex items-end gap-2'>
        <Textarea
          ref={textareaRef}
          value={inputValue}
          onChange={onInputChange}
          onKeyDown={onKeyDown}
          placeholder='Type your message here...'
          className='max-h-[200px] min-h-[60px] flex-1 resize-none rounded-2xl border border-gray-300 px-4 py-3 text-base shadow-sm focus-visible:border-primary focus-visible:ring-1 focus-visible:ring-primary'
          rows={1}
        />
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              size='icon'
              className='h-12 w-12 rounded-xl bg-primary text-primary-foreground shadow-md transition-all hover:bg-primary/90 hover:shadow-lg'
              onClick={onSendMessage}
              disabled={isLoading || inputValue.trim() === ''}
              aria-label='Send message'
            >
              <Send className='h-5 w-5' />
            </Button>
          </TooltipTrigger>
          <TooltipContent side='top'>
            <p>Send message</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
  )
}
