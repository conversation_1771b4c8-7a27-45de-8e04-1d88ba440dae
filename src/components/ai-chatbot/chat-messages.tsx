import { ChatMessagesList } from './chat-messages/ChatMessagesList'
import { Message } from './types'

interface ChatMessagesProps {
  messages: Message[]
  isLoading: boolean
  messagesEndRef: React.RefObject<HTMLDivElement>
  onFeedback: (messageId: string, feedback: 'like' | 'dislike') => void
  onCopyMessage: (messageId: string) => void
  onRetry?: () => void
}

export function ChatMessages(props: ChatMessagesProps) {
  return <ChatMessagesList {...props} />
}
