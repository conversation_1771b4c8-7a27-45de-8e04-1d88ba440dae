interface AIIconProps {
  className?: string
  pulseEffect?: boolean
  spinEffect?: boolean
  glowEffect?: boolean
  animationType?: 'pulse' | 'spin' | 'glow' | 'none'
}

export function AIIcon({
  className = 'h-6 w-6',
  pulseEffect = false,
  spinEffect = false,
  glowEffect = false,
  animationType = 'none',
}: AIIconProps) {
  // Determine animation class based on type or individual props
  const getAnimationClass = () => {
    if (animationType !== 'none') {
      switch (animationType) {
        case 'pulse':
          return 'animate-ai-icon-pulse'
        case 'spin':
          return 'animate-ai-icon-spin'
        case 'glow':
          return 'animate-ai-button-glow'
        default:
          return ''
      }
    } else {
      if (pulseEffect) return 'animate-pulse'
      if (spinEffect) return 'animate-ai-icon-spin'
      if (glowEffect) return 'animate-ai-button-glow'
      return ''
    }
  }

  return (
    <div className={`relative ${getAnimationClass()}`}>
      <svg
        xmlns='http://www.w3.org/2000/svg'
        viewBox='0 0 24 24'
        fill='none'
        stroke='currentColor'
        strokeWidth='1.75'
        strokeLinecap='round'
        strokeLinejoin='round'
        className={className}
      >
        {/* Modern AI brain with circuit patterns */}
        <path
          d='M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2z'
          strokeOpacity='0.2'
        />
        <path d='M12 7v10M7 12h10' strokeOpacity='0.6' />
        <path d='M9 9l6 6M15 9l-6 6' strokeOpacity='0.6' />
        <path d='M12 16a4 4 0 100-8 4 4 0 000 8z' />
        <path d='M12 2v4M12 18v4M2 12h4M18 12h4' strokeOpacity='0.4' />
        <path
          d='M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83'
          strokeOpacity='0.3'
        />
        <circle cx='12' cy='12' r='1' fill='currentColor' />
      </svg>
      {(pulseEffect || animationType === 'pulse') && (
        <div className='animate-slow-ping absolute inset-0 rounded-full bg-primary/20'></div>
      )}
      {animationType === 'spin' && (
        <div className='absolute inset-0 animate-pulse rounded-full bg-primary/10'></div>
      )}
    </div>
  )
}
