import ky from 'ky'
import { token } from '../components/ai-chatbot/token'

export const API_URL = 'https://agent-server-mastra.thedma04.workers.dev/api/' // 'http://localhost:4111/api/'

export const aiApi = ky.create({
  prefixUrl: API_URL,
  headers: {
    Accept: 'application/json',
    'Content-Type': 'application/json',
  },
  hooks: {
    beforeRequest: [
      async (request) => {
        request.headers.set('Authorization', `Bearer ${token}`)
      },
    ],
  },
})
