/**
 * Utility to extract token from URL query parameters
 */

/**
 * Gets the token from URL query parameters
 * @returns The token from URL or undefined if not present
 */
export function getTokenFromUrl(): string | undefined {
  if (typeof window === 'undefined') return undefined

  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get('token') || undefined
}

/**
 * Gets the token from URL or falls back to the provided default
 * @param defaultToken The default token to use if none is found in URL
 * @returns The token from URL or the default token
 */
export function getTokenWithFallback(): string {
  return getTokenFromUrl() || 'default-token'
}
