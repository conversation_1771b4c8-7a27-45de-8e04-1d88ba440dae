import typography from '@tailwindcss/typography'
import tailwindCssAnimate from 'tailwindcss-animate'
import defaultTheme from 'tailwindcss/defaultTheme'
import { fonts } from './src/config/fonts'

// Use destructuring with fallback to ensure fontFamily is defined
const { fontFamily = { sans: ['sans-serif'] } } = defaultTheme || {}

/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ['class'],
  content: ['./index.html', './src/**/*.{ts,tsx,js,jsx}'],
  safelist: fonts.map((font) => `font-${font}`),
  theme: {
    container: {
      center: 'true',
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      fontFamily: {
        inter: ['Inter', ...fontFamily.sans],
        manrope: ['Manrope', ...fontFamily.sans],
      },
      animation: {
        bounce: 'bounce 1s infinite',
        pulse: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'slow-ping': 'ping 3s cubic-bezier(0, 0, 0.2, 1) infinite',
        'ai-appear': 'ai-appear 0.8s ease-out forwards',
        'ai-message-appear':
          'ai-message-appear 1s cubic-bezier(0.16, 1, 0.3, 1) forwards',
        'ai-thinking': 'ai-thinking 1s ease-in-out infinite',
        'ai-icon-spin': 'ai-icon-spin 3s linear infinite',
        'ai-icon-pulse':
          'ai-icon-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'ai-button-glow': 'ai-button-glow 2s ease-in-out infinite',
      },
      keyframes: {
        bounce: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-25%)' },
        },
        pulse: {
          '0%, 100%': { opacity: 1 },
          '50%': { opacity: 0.5 },
        },
        'ai-appear': {
          '0%': { opacity: 0, transform: 'scale(0.6)' },
          '50%': { opacity: 0.7, transform: 'scale(1.1)' },
          '100%': { opacity: 1, transform: 'scale(1)' },
        },
        'ai-message-appear': {
          '0%': { opacity: 0, transform: 'translateY(20px)' },
          '60%': { opacity: 0.8, transform: 'translateY(-5px)' },
          '100%': { opacity: 1, transform: 'translateY(0)' },
        },
        'ai-thinking': {
          '0%': { transform: 'scale(0.9)' },
          '50%': { transform: 'scale(1.1)' },
          '100%': { transform: 'scale(0.9)' },
        },
        'ai-icon-spin': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
        'ai-icon-pulse': {
          '0%': { transform: 'scale(1)', opacity: 1 },
          '50%': { transform: 'scale(1.2)', opacity: 0.7 },
          '100%': { transform: 'scale(1)', opacity: 1 },
        },
        'ai-button-glow': {
          '0%': { boxShadow: '0 0 5px 0px hsl(var(--primary) / 0.5)' },
          '50%': { boxShadow: '0 0 20px 5px hsl(var(--primary) / 0.7)' },
          '100%': { boxShadow: '0 0 5px 0px hsl(var(--primary) / 0.5)' },
        },
      },
      transitionDelay: {
        0: '0ms',
        150: '150ms',
        300: '300ms',
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          1: 'hsl(var(--chart-1))',
          2: 'hsl(var(--chart-2))',
          3: 'hsl(var(--chart-3))',
          4: 'hsl(var(--chart-4))',
          5: 'hsl(var(--chart-5))',
        },
        sidebar: {
          DEFAULT: 'hsl(var(--sidebar-background))',
          foreground: 'hsl(var(--sidebar-foreground))',
          primary: 'hsl(var(--sidebar-primary))',
          'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
          accent: 'hsl(var(--sidebar-accent))',
          'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
          border: 'hsl(var(--sidebar-border))',
          ring: 'hsl(var(--sidebar-ring))',
        },
      },
    },
  },
  plugins: [tailwindCssAnimate, typography],
}
